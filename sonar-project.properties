sonar.projectKey=apis-core
sonar.projectName=APIs Core
sonar.projectVersion=1.0
sonar.host.url=https://sonarqube.batuta.services/

sonar.sources=.
sonar.inclusions=apps/api-worker/src/**/*,apps/api-users/src/**/*,shared/src/**/*

sonar.exclusions=**/node_modules/**,\
                 **/dist/**,\
                 **/build/**,\
                 **/coverage/**,\
                 **/*.spec.ts,\
                 **/*.test.ts,\
                 **/*.d.ts,\
                 **/package.json,\
                 **/package-lock.json,\
                 **/pnpm-lock.yaml,\
                 **/*.config.js,\
                 **/*.config.ts

sonar.typescript.node=node
sonar.typescript.tsconfigPaths=tsconfig.json,shared/tsconfig.json,apps/api-users/tsconfig.json,apps/api-worker/tsconfig.json

sonar.sourceEncoding=UTF-8

sonar.verbose=true