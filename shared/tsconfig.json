{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "rootDir": "./src", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "strict": true, "strictPropertyInitialization": false, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": ".", "paths": {"@shared/*": ["./src/*"]}, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./types", "../node_modules/@types"], "tsBuildInfoFile": "./dist/.tsbuildinfo"}, "include": ["src/**/*", "types/**/*", "src/**/*.json"], "exclude": ["node_modules", "dist", "**/*.spec.ts"]}