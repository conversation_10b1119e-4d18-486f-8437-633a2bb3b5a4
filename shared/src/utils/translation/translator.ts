import email from './locales/email.json';
import report from './locales/report.json';
import countries from './locales/countries.json';

import { TLanguage } from '@shared/types/languages.types';

const translations = {
  email,
  report,
};

type Translations = keyof typeof translations;

const countryLocales = countries;

/**
 * Generic type that allows typescript to perform a static check of the translation
 * files when the t function is called by telling it how to navigate the translations
 * object to find the correct object
 */
export type ValidKey<T extends Translations, L extends TLanguage> = T extends 'email'
  ? L extends 'en'
    ? keyof (typeof email)['en']
    : keyof (typeof email)['es']
  : L extends 'en'
    ? keyof (typeof report)['en']
    : keyof (typeof report)['es'];

/**
 * Returns the specified translation
 *
 * @param {Translations} [module] Translation module to use (i.e. 'email'). If not specified, the default is used.
 * @param {TLanguage} [language] Language to use (i.e. 'en'). If not specified, the default is used.
 * @param {string} key Key of the translation.
 * @param {object} [values] Dynamic values to be inserted in the translation
 * @returns {string} Translation
 */
export const t = <T extends Translations, L extends TLanguage>(
  module: T = 'email' as T,
  language: L = 'en' as L,
  key: ValidKey<T, L>,
  values: { [key: string]: any } = {}
) => {
  // Try to use required language, and default to english
  const translationValues = translations[module][language] || translations.email.en;

  let text = (translationValues[key as keyof typeof translationValues] as string) || key;

  // Replace the placeholders in the translation with the values passed
  Object.keys(values).forEach((v) => {
    text = text.replace(new RegExp(`{{${v}}}`, 'gm'), values[v]);
  });

  return text;
};

/**
 * Returns the specified translation
 *
 * @param {string} [language] Language to use (i.e. 'en'). If not specified, the default is used
 * @param {string} countryCode Country code
 * @returns {string} Country name
 */
export const t_country = (language: TLanguage = 'en', countryCode: string): string => {
  // Try to use required language, and default to english
  const translationValues = countryLocales[language] || countries.en;

  const country = translationValues.find((c) => c.code.toUpperCase() === countryCode.toUpperCase());

  return country ? country.name : countryCode.toUpperCase();
};
