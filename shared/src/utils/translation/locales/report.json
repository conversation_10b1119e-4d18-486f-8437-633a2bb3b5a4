{"en": {"period": "Period", "batutaReport": "Batuta Report", "hostsSectionTile": "Hosts Information", "hostsSectionIntroduction": "Below is a summary of the current state of your company's IT infrastructure. In this section, you will find the total number of hosts, the hosts added in the last 30 days, and information regarding the geographic and operating system distribution of the hosts.", "geoDistribution": "Geographic distribution", "country": "Country", "nHosts": "Nº of hosts", "hostsStatus": "Agents Summary", "status": "Status", "hostsOsDistribution": "Operating Systems", "os": "OS", "windows": "Windows", "linux": "Linux", "ios": "iOS", "android": "Android", "macOs": "macOS", "totalHosts": "Total Hosts", "hostsAddedInTheLast30Days": "Hosts added in the last 30 days", "updateOSSectionTitle": "OS Updates", "hostsWithUpdates": "Hosts whit Updates", "hostsWithSecurityUpdates": "Hosts whit <PERSON> Updates", "hostsPendingUpdates": "Hosts without pending updates", "totalUpdateByCategory": "Host Updates", "Update Type": "Update Type", "totalUpdates": "Total Updates", "updates": "Updates", "securityUpdates": "Security Updates", "numberOfUpdates": "Number of Updates", "daysExposed": "Days Exposed", "date": "Date", "last60DaysUpdatesByCategory": "Last 60 Days Updates by Category", "technologyCoverageSectionTitle": "Technology coverage", "technologyCoverageReportIntroduction": "This section evaluates the deployment and utilization of key cybersecurity tools across your infrastructure. Below is a summary of the coverage and licensing status of the contracted technologies that have been integrated into Batuta. In this section, you can see the number of hosts covered by the registered technologies, and view a snapshot of where licenses are being underutilized or overutilized.", "statusOfContractedTechnologies": "Technology and Licensing Status", "technology": "Technology", "count": "Count", "licenses": "Licenses", "running": "Running", "stopped": "Stopped", "coverageStatus": "Coverage Status", "hostsRunning": "Hosts Running", "hostsStopped": "Hosts Stopped", "hostsWithoutTechnology": "Uninstalled", "percentageCoverage": "% Coverage", "resumen1Technology": "Currently, ", "resumen2Technology": "of your hosts are vulnerable to security breaches because they do not have any technologies installed and running.", "complianceSectionTitle": "Hardening Benchmark", "complianceReportIntroduction": "This section provides a summary of the overall status of the company based on the compliance with security policies defined and applied through Batuta. You will also find a detailed overview of the various security policies applied, including information such as the current status of each policy, a timeline of its evolution, a summary of the evaluated controls, and results at the control level.", "hostsByCompliance": "Latest Results", "yes": "yes", "no": "no", "infrastructure": "Infrastructure", "compliant": "Compliant", "nonCompliant": "Not Compliant", "nonCompliance": "Non Compliant", "reached": "Reached", "not_reached": "Not Reached", "targeted": "Target", "target": "Target", "not_targeted": "Not Targeted", "resumen1Compliance": "", "resumen2Compliance": "of the covered hosts comply with all assigned policies, and", "resumen3Compliance": "of the hosts in the infrastructure are subject to a compliance policy.", "resumen3Policy": "of the target hosts were reached.", "policyStatus": "Policy Status", "policy": "Policy", "policies": "Policies", "enabled": "Enabled", "disabled": "Disabled", "summary": "Summary", "compliantLast60Days": "Compliance Evolution", "complianceControlSummary": "Payload Distribution", "compliantHistory": "Compliant History", "compliance": "Compliance", "not_compliant": "Not Compliant", "controls": "Controls", "numberOfControls": "Number of Controls", "totalControls": "Total Controls", "failedControlsBySeverity": "Failed Controls by Severity", "high": "High severity controls", "medium": "Medium severity controls", "low": "Low severity controls", "averageCompliance": "Average Compliance", "top5High": "Top 5 High Severity Controls Failed", "top5Medium": "Top 5 Medium Severity Controls Failed", "top5Low": "Top 5 Low Severity Controls Failed", "control": "Control", "evaluated": "Evaluated", "fulfilled": "Fulfilled", "nonFulfilled": "Not Fulfilled", "controlsComplianceBySeverity": "Results By Severity of Controls", "inventorySectionTitle": "Application Inventory", "inventoryReportIntroduction": "This section provides a general overview of the results of the application control policies. You will also find a detailed breakdown of each policy, including the compliance level of affected hosts, the top 5 hosts with the lowest rule compliance, and the top 5 most violated rules within each policy.", "inventoryPoliciesStatus": "Policy Status", "inventoryPoliciesHostsStatus": "Inventory Policies Hosts Status", "noInventory": "No Inventory", "hostsAffected": "Hosts Affected", "hostsNotAffected": "Hosts Not Affected", "levelCompliance": "Compliance Level", "minComplianceLevel": "Percentile", "hostsComplyFewestRules": "Top 5 Hosts with Lowest Rule Compliance", "nonApproved": "Non-Approved Rules", "totalRules": "Total Rules", "percentageCompliantRules": "% Compliant Rules", "unfulfilledRules": "Top 5 Most Failed Rules", "hostsPassed": "Hosts Passed", "rule": "Rule", "ruleCondition": "Condition", "allowed": "Allowed", "notAllowed": "Not Allowed", "percentageHostsCompliant": "% Hosts Compliant", "percentage_reached": "% Reached", "percentage_compliant": "% Compliant", "deploymentsSectionTitle": "Deployment Summary", "deploymentsReportIntroduction": "This section provides a summary of all deployments executed through Batuta. You can view the total number of affected devices compared to the total number of hosts in the infrastructure, along with a list of the 10 devices most impacted by actions executed via Batuta.", "deploymentsByOrigin": "Deployments by Origin", "origin": "Origin", "numberOfActions": "Nº Actions", "responseActions": "Response Actions", "responseLibrary": "Response Library", "deployments": "Deployments", "reboots": "Reboots", "uninstalls": "Uninstalls", "hostsAffectedLast30Days": "Host Affected in last 30 days", "affected": "Affected", "nonAffected": "Not Affected", "mostAffectedHosts": "Top 10 Most Affected Hosts", "hostname": "Hostname", "mostExecutedActions": "Top 5 Most Executed Actions", "actionName": "Action Name", "totalExecutions": "Total Executions", "success": "Success", "failed": "Failed", "unknown": "Unknown", "queued": "Queued", "cancel": "Cancel", "actions": "Actions", "actionsExecuted": "Actions Executed", "hosts": "Hosts", "host": "Host", "confidential": "This information is confidential for ", "mail": "<EMAIL>", "web": "metabaseq.com", "type": "Type", "hostWithSecurityUpdates": "Hosts with Security Updates", "hostWithUpdates": "Hosts with Updates", "hostUpToDate": "Hosts Up to Date", "index": "Content", "levelOfProactivityTitle": "Proactivity level", "levelOfProactivityIntroduction": "In this section, you will find the current proactivity level of your company. You will also see the distribution of teams based on their proactivity level, and finally, a detailed breakdown by areas, along with a summary table of the values.", "proactivitySummary": "A thorough analysis of your different areas has shown that your proactivity level is", "globalScore": "Global Score", "qualification": "Qualification", "percentage": "Percentage", "proactivityHostDistribution": "Hosts by Proactivity level", "proactivityByArea": "Proactivity by Area", "area": "Area", "osUpdates": "OS Updates", "platform": "Technology Coverage", "globalScoreDescription": "By performing a detailed calculation, taking into account each of the enabled areas and applying the respective weight to each, the overall score of your company is", "globalScoreDescription2": "with an average percentage of", "globalScoreDescription3": "across all areas.", "High": "High", "Medium": "Medium", "Low": "Low", "total": "Total", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "N/A": "N/A", "LOP.INTRO.TITLE": "Introduction to Proactivity Level Score", "LOP.INTRO.P1": "This report provides a comprehensive assessment of your organization's cybersecurity posture, focusing on your proactivity level. Proactivity in cybersecurity is essential to mitigating risks and protecting sensitive information. It involves actively identifying, addressing, and preventing potential threats before they can cause significant harm.", "LOP.INTRO.P2": "By analyzing your organization's performance across key areas such as compliance, technology coverage, and OS updates, we have calculated a proactivity score. This score serves as a valuable indicator of your organization's overall security readiness. A higher proactivity score signifies a more robust security posture, while a lower score indicates areas that require immediate attention.", "LOP.INTRO.P3": "Understanding your proactivity level is crucial for making informed decisions and prioritizing security initiatives. This report will provide insights into your organization's strengths, weaknesses, and areas for improvement. By taking proactive measures to address the identified issues, you can enhance your security posture and safeguard your organization from cyber threats.", "LOP.KEY_TERMS.TITLE": "Key Terms", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.TITLE": "Proactivity Level", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.DESCRIPTION": "A metric that assesses an organization's proactive approach to cybersecurity.", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.BP1": "It is calculated based on factors such as compliance with security policies, deployment of security technologies, and timely application of updates.", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.BP2": "A higher proactivity level indicates a stronger security posture and reduced risk", "LOP.KEY_TERMS.PROACTIVITY_SCORE.TITLE": "Proactivity Score", "LOP.KEY_TERMS.PROACTIVITY_SCORE.DESCRIPTION": "A numerical representation of the proactivity level, typically expressed as a percentage.", "LOP.KEY_TERMS.PROACTIVITY_SCORE.BP1": "It is derived from the evaluation of various cybersecurity metrics and benchmarks.", "LOP.KEY_TERMS.PROACTIVITY_SCORE.BP2": "A higher score signifies a more proactive approach to cybersecurity.", "LOP.KEY_TERMS.GRADE_RANGES.TITLE": "Grade Ranges", "LOP.KEY_TERMS.GRADE_RANGES.DESCRIPTION": "A categorization of proactivity scores into specific grades (e.g., A, B, C, D, E).", "LOP.KEY_TERMS.GRADE_RANGES.BP1": "Each grade range corresponds to a particular level of proactivity and associated risk level.", "LOP.KEY_TERMS.GRADE_RANGES.BP2": "For example, a grade of \"A\" indicates excellent proactivity, while a grade of \"E\" signifies critical issues requiring immediate attention.", "LOP.KEY_TERMS.HOST.TITLE": "Host", "LOP.KEY_TERMS.HOST.DESCRIPTION": "A device connected to a network, such as a computer or server.", "LOP.KEY_TERMS.CIS_CONTROLS.TITLE": "CIS Controls", "LOP.KEY_TERMS.CIS_CONTROLS.DESCRIPTION": "A set of cybersecurity best practices and benchmarks developed by the Center for Internet Security (CIS). These controls provide a framework for organizations to improve their security posture.", "LOP.GLOBAL_SCORE.TITLE": "Global Score", "LOP.GLOBAL_SCORE.P1": "Based on the data collected by Batuta via the {{totalNumber}} agents across three assessment areas (Compliance, Technology Coverage, and OS updates) the overall score of your company is {{grade}} with an average score of {{avgScore}}% across all three areas.", "OS_UPDATE.TITLE": "OS Updates", "OS_UPDATE.DESCRIPTION": "This section provides a summary of your infrastructure's update status, including the number of pending updates (operating system and security) and a classification of hosts based on whether updates are complete, pending, or security-related.", "OS_UPDATE.KEY_FINDINGS": "Key Findings", "OS_UPDATE.KEY_FINDINGS.BP1": "{{percentageHostsWithUpdates}}% of hosts are pending OS updates and {{percentageHostsWithSecurityUpdates}}% of hosts are pending Security updates  ", "OS_UPDATE.KEY_FINDINGS.HORIZONTAL_BAR_CHART.TITLE": "Distribution of OS Updates by OS", "OS_UPDATE.KEY_FINDINGS.HORIZONTAL_BAR_CHART.NUM_UPDATES": "Number of updates", "OS_UPDATE.KEY_FINDINGS.BP2": "There are {{PERCENTAGE_UPDATES_OVER_30_DAYS}}% pending OS updates or Security updates for over 30 days", "OS_UPDATE.RECOMMENDED_ACTIONS": "Recommended Actions", "OS_UPDATE.RECOMMENDED_ACTIONS.BP1": "Ensure all security updates are applied promptly to reduce vulnerabilities", "OS_UPDATE.RECOMMENDED_ACTIONS.BP2": "Establish a regular update schedule to maintain consistency.", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.TITLE": "Pending OS and Security Updates by Age", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.X.AXIS.DAYS": "Days", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.X.TITLE.TIME_PERIOD": "Time Period (Days Past Due)", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.Y.TITLE.NUMBER_OF_UPDATES": "Number of Updates", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.LABEL.OS_UPDATES": "OS Updates", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.LABEL.SECURITY_UPDATES": "Security Updates", "COMPLIANCE": "Compliance", "TECHNOLOGY_COVERAGE": "Technology Coverage", "OS_UPDATES": "OS Updates", "SUMMARY.TITLE": "Summary of Findings", "SUMMARY.GLOBAL_SCORE.TITLE": "Global Score", "SUMMARY.GLOBAL_SCORE.P1": "Based on the data collected by Batuta via the {{hostsCount}} agents across three assessment areas (Compliance, Technology Coverage, and OS updates) the overall score of your company is {{qualification}} with an average score of {{score}}% across all three areas.", "SUMMARY.GLOBAL_SCORE.A": "The current overall proactivity score of A indicates a high level of readiness. Your company demonstrates good practices in the areas of Compliance, Technology Coverage, and OS Updates.", "SUMMARY.GLOBAL_SCORE.B": "The current overall proactivity score of B indicates a solid foundation in cybersecurity readiness. While some areas still require improvement, the organization demonstrates a strong foundation in cybersecurity practices.", "SUMMARY.GLOBAL_SCORE.C": "The current overall proactivity score of C indicates a moderate readiness. While some areas demonstrate good practices, others require immediate attention to enhance the organization’s cybersecurity posture.", "SUMMARY.GLOBAL_SCORE.D": "The current overall proactivity score of D indicates a limited level of readiness. While some areas demonstrate basic practices, significant gaps exist in critical areas such as Windows CIS controls, technology coverage, and OS updates. Immediate attention is required to address these vulnerabilities and improve the organization's cybersecurity posture.", "SUMMARY.GLOBAL_SCORE.E": "The current overall proactivity score of E indicates a minimal level of readiness. Significant deficiencies exist in multiple areas, including Windows CIS controls, technology coverage, and OS updates. Immediate and substantial action is required to address these critical vulnerabilities and improve the organization's cybersecurity posture.", "SUMMARY.GLOBAL_SCORE.F": "Due to a lack of reported data on the organization's systems, a comprehensive assessment of its cybersecurity posture is not possible at this time. A thorough evaluation requires accurate and timely data on key areas such as Windows CIS controls, technology coverage, and OS updates.", "SUMMARY.KEY_FINDINGS.TITLE": "Key Findings", "SUMMARY.KEY_FINDINGS.A.TITLE": "Strengths", "SUMMARY.KEY_FINDINGS.A.COMPLIANCE": "Scored A ({{score}}%), indicating an exceptional adherence to security policies, with minimal non-compliance issues, indicating a high level of protection and compliance with the Windows CIS controls.", "SUMMARY.KEY_FINDINGS.A.TECHNOLOGY_COVERAGE": "Scored A ({{score}}%), indicating exceptional technology coverage, with all critical security tools, including EDR, patching, and vulnerability management, effectively deployed and actively utilized across all endpoints.", "SUMMARY.KEY_FINDINGS.A.OS_UPDATES": "Scored A ({{score}}%), demonstrating exceptional OS update management, with all devices on the network consistently updated with the latest operating system and security patches.", "SUMMARY.KEY_FINDINGS.B.TITLE": "Strengths", "SUMMARY.KEY_FINDINGS.B.COMPLIANCE": "Scored B ({{score}}%), indicating a solid foundation in security policy compliance, with some minor non-compliance issues that require attention, indicating a well-protected infrastructure with room for improvement.", "SUMMARY.KEY_FINDINGS.B.TECHNOLOGY_COVERAGE": "Scored B ({{score}}%), demonstrating a solid foundation in technology coverage, with most critical security tools, including EDR, patching, and vulnerability management, effectively deployed and utilized across a majority of endpoints.", "SUMMARY.KEY_FINDINGS.B.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.C.TITLE": "Areas of Improvement", "SUMMARY.KEY_FINDINGS.C.COMPLIANCE": "Scored C ({{score}}%), indicating there are some compliance gaps, particularly in high-risk areas, indicating a need for attention to improve security related to security controls.", "SUMMARY.KEY_FINDINGS.C.TECHNOLOGY_COVERAGE": "Scored C ({{score}}%), indicating moderate technology coverage, with some critical security tools, such as EDR, patching, and vulnerability management, deployed but with gaps in coverage and utilization across endpoints.", "SUMMARY.KEY_FINDINGS.C.OS_UPDATES": "Scored C ({{score}}%), demonstrating a moderate level of OS update management, with some devices having outdated operating systems but with security updates generally applied.", "SUMMARY.KEY_FINDINGS.D.TITLE": "Areas of Improvement", "SUMMARY.KEY_FINDINGS.D.COMPLIANCE": "Scored D ({{score}}%), indicating significant compliance gaps in critical areas related to Windows CIS controls. Urgent action is required to improve the level of proactivity and mitigate the associated security risks.", "SUMMARY.KEY_FINDINGS.D.TECHNOLOGY_COVERAGE": "Scored D ({{score}}%), indicating limited technology coverage, with significant gaps in the deployment and utilization of critical security tools such as EDR, patching, and vulnerability management, leaving many endpoints vulnerable.", "SUMMARY.KEY_FINDINGS.D.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.E.TITLE": "Areas of Improvement", "SUMMARY.KEY_FINDINGS.E.COMPLIANCE": "Scored E ({{score}}%), indicating severe compliance issues related to Windows CIS controls. This critical situation requires immediate review and action to mitigate the associated security risks.", "SUMMARY.KEY_FINDINGS.E.TECHNOLOGY_COVERAGE": "Scored E ({{score}}%), indicating minimal technology coverage, with critical security tools such as EDR, patching, and vulnerability management either not deployed or not effectively utilized, leaving the majority of endpoints severely vulnerable.", "SUMMARY.KEY_FINDINGS.E.OS_UPDATES": "Scored E ({{score}}%), indicating a critical lack of OS update management, with many devices severely outdated and lacking critical security updates, requiring immediate attention.", "SUMMARY.KEY_FINDINGS.F.TITLE": "", "SUMMARY.KEY_FINDINGS.F.COMPLIANCE": "", "SUMMARY.KEY_FINDINGS.F.TECHNOLOGY_COVERAGE": "", "SUMMARY.KEY_FINDINGS.F.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.N/A.TITLE": "", "SUMMARY.KEY_FINDINGS.N/A.COMPLIANCE": "", "SUMMARY.KEY_FINDINGS.N/A.TECHNOLOGY_COVERAGE": "", "SUMMARY.KEY_FINDINGS.N/A.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.TITLE": "Recommended Actions", "SUMMARY.RECOMMENDED_ACTIONS.A.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.B.TITLE": "Recommended Actions", "SUMMARY.RECOMMENDED_ACTIONS.B.COMPLIANCE": "Analyze the Batuta dashboards in the Compliance module and run the available scripts in the Response Library to remedy non-compliance issues related to CIS controls.", "SUMMARY.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "Use Batuta to quickly identify the devices that are missing critical cybersecurity tools, and use the New Deployment module to complete the installation on all devices", "SUMMARY.RECOMMENDED_ACTIONS.B.OS_UPDATES": "Use Batuta to analyze the missing updates, identify root cause of any failed updates, and determine the devices that need immediate attention. Then utilize your patching technology to deploy the approved patches.", "SUMMARY.RECOMMENDED_ACTIONS.C.TITLE": "Recommended Actions", "SUMMARY.RECOMMENDED_ACTIONS.C.COMPLIANCE": "Analyze the Batuta dashboards in the Compliance module and run the available scripts in the Response Library to remedy non-compliance issues related to CIS controls.", "SUMMARY.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "Use Batuta to quickly identify the devices that are missing critical cybersecurity tools, and use the New Deployment module to complete the installation on all devices", "SUMMARY.RECOMMENDED_ACTIONS.C.OS_UPDATES": "Use Batuta to analyze the missing updates, identify root cause of any failed updates, and determine the devices that need immediate attention. Then utilize your patching technology to deploy the approved patches.", "SUMMARY.RECOMMENDED_ACTIONS.D.TITLE": "Recommended Actions", "SUMMARY.RECOMMENDED_ACTIONS.D.COMPLIANCE": "Analyze the Batuta dashboards in the Compliance module and run the available scripts in the Response Library to remedy non-compliance issues related to CIS controls.", "SUMMARY.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "Use Batuta to quickly identify the devices that are missing critical cybersecurity tools, and use the New Deployment module to complete the installation on all devices", "SUMMARY.RECOMMENDED_ACTIONS.D.OS_UPDATES": "Use Batuta to analyze the missing updates, identify root cause of any failed updates, and determine the devices that need immediate attention. Then utilize your patching technology to deploy the approved patches.", "SUMMARY.RECOMMENDED_ACTIONS.E.TITLE": "Recommended Actions", "SUMMARY.RECOMMENDED_ACTIONS.E.COMPLIANCE": "Analyze the Batuta dashboards in the Compliance module and run the available scripts in the Response Library to remedy non-compliance issues related to CIS controls.", "SUMMARY.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "Use Batuta to quickly identify the devices that are missing critical cybersecurity tools, and use the New Deployment module to complete the installation on all devices", "SUMMARY.RECOMMENDED_ACTIONS.E.OS_UPDATES": "Use Batuta to analyze the missing updates, identify root cause of any failed updates, and determine the devices that need immediate attention. Then utilize your patching technology to deploy the approved patches.", "SUMMARY.RECOMMENDED_ACTIONS.F.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "", "HOSTS_BY_PROACTIVITY.TITLE": "Hosts by Proactivity Level", "HOSTS_BY_PROACTIVITY.P1": "The breakdown of hosts by proactivity level is shown below. Currently, {{okayHostsPercent}}% of hosts ({{okayHostsCount}} out of {{totalHosts}}) are within an acceptable proactivity level with a score of A or B. The remaining {{hostsNeedAttention}}% of hosts (with scores of C through E) need immediate attention.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.TITLE": "Recommended Actions", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.COMPLIANCE": "", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "Strengthen your security controls posture by creating a plan to reach 100% compliance with CIS and internal security policies. Use the scripts available in the Batuta Response Library for easy remediation.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "Fully deploy and activate licensed technologies to enhance coverage, particularly in areas such as EDR and anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "Prioritize the remediation efforts on the {{highRiskPercentage}}% of high-risk hosts classified as D and E, ensuring all are updated and compliant.", "PROACTIVITY_BY_AREA.TITLE": "Proactivity by Assessment Area", "PROACTIVITY_BY_AREA.P1": "This section provides a detailed assessment of your organization's cybersecurity posture based on the proactivity level of each assessment area: Compliance, Technology Coverage, and OS and Security Updates.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.TITLE": "Key Findings", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.COMPLIANCE": "Your organization demonstrates exceptional adherence to security policies, with minimal non-compliance issues.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.COMPLIANCE": "Your organization demonstrates a solid foundation in security policy compliance, with some minor non-compliance issues that require attention.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.COMPLIANCE": "Your organization has some compliance gaps, particularly in high-risk areas. Addressing these issues is crucial to improving your security posture.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.COMPLIANCE": "Your organization has significant compliance gaps, particularly in critical areas such as CIS controls. Immediate action is required to address these issues.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.COMPLIANCE": "Your organization has severe compliance issues, posing significant risks to your security posture. Immediate and comprehensive action is required to address these issues.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.COMPLIANCE": "Due to insufficient data, a comprehensive assessment of your organization's compliance posture is not possible at this time.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.COMPLIANCE": "Due to compatibility, a comprehensive assessment of your organization's compliance posture is not possible at this time.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.TECHNOLOGY_COVERAGE": "Your organization has effectively deployed and utilized key cybersecurity tools across all hosts, ensuring comprehensive protection.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.TECHNOLOGY_COVERAGE": "Your organization has deployed key cybersecurity tools to a significant portion of hosts, but there are opportunities to improve coverage and utilization.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.TECHNOLOGY_COVERAGE": "Your organization has deployed some cybersecurity tools, but there are significant gaps in coverage, leaving many hosts unprotected.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.TECHNOLOGY_COVERAGE": "Your organization has limited deployment of cybersecurity tools, leaving many hosts vulnerable to attacks.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.TECHNOLOGY_COVERAGE": "Your organization has minimal deployment of cybersecurity tools, leaving most hosts unprotected. Urgent action is needed to improve coverage and protect your organization.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.TECHNOLOGY_COVERAGE": "Due to insufficient data, a comprehensive assessment of your organization's technology coverage is not possible at this time.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.TECHNOLOGY_COVERAGE": "Due to compatibility, a comprehensive assessment of your organization's technology coverage is not possible at this time.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.OS_UPDATES": "Your organization maintains a strong update management process, with all hosts up-to-date with the latest OS and security patches.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.OS_UPDATES": "Your organization has a generally effective update management process, but some hosts may have outdated OS or security patches.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.OS_UPDATES": "Your organization has a moderate level of update management, with some hosts having outdated OS or security patches.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.OS_UPDATES": "Your organization has a poor update management process, with many hosts having outdated OS or security patches.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.OS_UPDATES": "Your organization has a critical lack of update management, with many hosts severely outdated and highly vulnerable to attacks. Immediate action is necessary to address these vulnerabilities.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.OS_UPDATES": "Due to insufficient data, a comprehensive assessment of your organization's OS and security update status is not possible at this time.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.OS_UPDATES": "Due to compatibility, a comprehensive assessment of your organization's OS and security update status is not possible at this time.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.TITLE": "Recommended Actions", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.COMPLIANCE": "The non-compliant hosts should be analyzed and remedied as soon as possible using Batuta pre-loaded scripts found in the Response Library.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.COMPLIANCE": "The non-compliant hosts should be analyzed and remedied as soon as possible using Batuta pre-loaded scripts found in the Response Library.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.COMPLIANCE": "The non-compliant hosts should be analyzed and remedied as soon as possible using Batuta pre-loaded scripts found in the Response Library.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.COMPLIANCE": "The non-compliant hosts should be analyzed and remedied as soon as possible using Batuta pre-loaded scripts found in the Response Library.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.COMPLIANCE": "The non-compliant hosts should be analyzed and remedied as soon as possible using Batuta pre-loaded scripts found in the Response Library.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.COMPLIANCE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.OS_UPDATES": "We recommend the analysis of the missing updates and push proper OS and Security updates on identified machines.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.OS_UPDATES": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.OS_UPDATES": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.OS_UPDATES": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.OS_UPDATES": "To strengthen your cybersecurity posture, and take advantage of your license investment, the pre-approved cybersecurity tools should be deployed to all hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.OS_UPDATES": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "", "DETAILED_FINDINGS": "Detailed Findings", "HOSTS_SECTION.TITLE": "Host Information", "HOSTS_SECTION.P1": "Below is a summary of the current state of your company's IT infrastructure. In this section, you will find the total number of hosts, the hosts added in the last 30 days, and information regarding the geographic and operating system distribution of the hosts.", "HOSTS_SECTION.BAR_CHART_TITLE": "Distribution of Hosts by OS", "HOSTS_SECTION.BAR_CHART_NUMBER_OF_HOSTS": "Number of Hosts", "APPENDIX.TITLE": "A<PERSON>ndix", "COMPLIANCE.KEY_FINDINGS.TITLE": "Key Findings", "COMPLIANCE.KEY_FINDINGS.P1": "The following are the Security Policies Applied and Enabled through Batuta:", "COMPLIANCE.KEY_FINDINGS.P2": "The following policies are enabled but are applied to invalid groups:", "SECURITY_POLICY_DETAILS": "Security Policy Details", "SOFTWARE_INVENTORY_POLICY_DETAILS": "Software Inventory Policy Details", "TECHNOLOGY_COVERAGE.PERCENT_OF_ACTIVE_LICENSES": "{{techName}}: {{percentActive}}% of licenses are active.", "TECHNOLOGY_COVERAGE.HOSTS_NO_TECH": "Currently, {{percentNoTech}}% of hosts are vulnerable to security breaches because they do not have any technologies installed and running, making them highly vulnerable to breaches and could compromise the entire infrastructure. We recommend the immediate deployment of missing technologies to all the endpoints."}, "es": {"period": "<PERSON><PERSON><PERSON>", "batutaReport": "Reporte de Batuta", "hostsSectionTile": "Información de equipos", "hostsSectionIntroduction": "A continuación, se muestra un resumen sobre el estado actual de la infraestructura de TI de su compañía. En este apartado podrá observar la cantidad total de equipos, los equipos agregados en los últimos 30 días, y la información relacionada con la distribución geográfica y el sistema operativo de los equipos.", "geoDistribution": "Distribución geográfica", "country": "<PERSON><PERSON>", "nHosts": "Nº de hosts", "hostsStatus": "Resumen de agentes", "status": "Estado", "hostsOsDistribution": "Sistemas operativos", "os": "SO", "windows": "Windows", "linux": "Linux", "ios": "iOS", "android": "Android", "macOs": "macOS", "totalHosts": "Total de hosts", "hostsAddedInTheLast30Days": "Hosts añadidos en los últimos 30 días", "updateOSSectionTitle": "Actualizaciones de SO", "updateOSReportIntroduction": "Este resumen muestra el estado de las actualizaciones de su infraestructura, incluyendo el número de actualizaciones pendientes (básicas y de seguridad) y la clasificación de hosts según tengan actualizaciones completas, pendientes o de seguridad pendientes.", "hostsUpdates": "Actualizaciones pendientes", "hostsWithUpdates": "Hosts con actualizaciones", "hostsWithSecurityUpdates": "Hosts con actualizaciones de seguridad", "hostsPendingUpdates": "Hosts sin actualizaciones pendientes", "totalUpdateByCategory": "Actualizaciones por equipos", "Update Type": "Tipo de actualización", "totalUpdates": "Total de actualizaciones", "updates": "Actualizaciones", "securityUpdates": "Actualizaciones de seguridad", "numberOfUpdates": "Número de actualizaciones", "daysExposed": "Días expuestos", "date": "<PERSON><PERSON>", "last60DaysUpdatesByCategory": "Actualizaciones de los últimos 60 días", "technologyCoverageSectionTitle": "Cobertura tecnológica", "technologyCoverageReportIntroduction": "Esta sección evalúa el despliegue y utilización de herramientas de ciberseguridad claves a través de su infraestructura. A continuación encontrarás un resumen de la cobertura y estatus de utilización de licencias para las tecnologías registradas dentro de Batuta. Esta sección muestra el número de hosts  cubiertos por las tecnologías registradas y un vistazo sobre el aprovechamiento (o no) de las licencias adquiridas para cada una.", "statusOfContractedTechnologies": "Estado de tecnologías y licenciamiento", "technology": "Tecnología", "count": "Cantidad", "licenses": "Licencias", "running": "En ejecución", "stopped": "Detenidos", "coverageStatus": "Estado de cobertura", "hostsRunning": "Hosts en ejecución", "hostsStopped": "Hosts detenidos", "hostsWithoutTechnology": "Desinstal<PERSON>s", "percentageCoverage": "% Cobertura", "resumen1Technology": "Actualmente, ", "resumen2Technology": "de sus equipos son susceptibles a ser vulnerados debido a que no poseen ninguna tecnología instalada y en ejecución.", "complianceSectionTitle": "Estado de cumplimiento", "complianceReportIntroduction": "Esta sección contiene un resumen del estado de cumplimiento global de la compañía tomando como referencia las políticas de cumplimiento configuradas en Batuta. También encontrará un resumen comprensivo de las políticas de seguridad aplicadas incluyendo información como estatus actual, una línea del tiempo con su evolución, un resumen de los controles evaluados y los resultados a nivel control.", "hostsByCompliance": "Últimos resultados", "yes": "Sí", "no": "No", "infrastructure": "Infraestructura", "compliance": "<PERSON><PERSON>le", "not_compliant": "No cumple", "reached": "Alcanzados", "not_reached": "No alcanzados", "target": "Cubierto", "targeted": "Cubierto", "not_targeted": "No cubierto", "percentage_reached": "% Alcanzados", "percentage_compliant": "% Cumplen", "resumen1Compliance": "El", "resumen2Compliance": "de los equipos alcanzados cumple con todas las políticas asignadas, y el", "resumen3Compliance": "de los equipos de la infraestructura está sujeto a una política de cumplimiento.", "resumen3Policy": "de los equipos objetivo fueron alcanzados.", "policyStatus": "Estado de políticas", "policy": "Política", "policies": "Políticas", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "summary": "Resumen", "compliantLast60Days": "Evolución de cumplimiento", "complianceControlSummary": "Distribución de controles", "compliantHistory": "Historial de cumplimiento", "compliant": "Cumplimiento", "nonCompliant": "No cumplimiento", "nonCompliance": "Incumple", "controls": "Controles", "numberOfControls": "Número de controles", "totalControls": "Total de controles", "failedControlsBySeverity": "Controles fallidos por severidad", "high": "Controles de alta severidad", "medium": "Controles de severidad media", "low": "Controles de baja severidad", "averageCompliance": "Cumplimiento promedio", "top5High": "Top 5 controles fallidos de alta severidad", "top5Medium": "Top 5 controles fallidos de severidad media", "top5Low": "Top 5 controles fallidos de baja severidad", "control": "Control", "evaluated": "<PERSON><PERSON><PERSON>", "fulfilled": "<PERSON><PERSON><PERSON><PERSON>", "nonFulfilled": "No Cumplido", "controlsComplianceBySeverity": "Resultados por severidad de controles", "inventorySectionTitle": "Inventario de aplicaciones", "inventoryReportIntroduction": "Esta sección ofrece un resumen general de los resultados de las políticas de control de aplicaciones. También encontrará un desglose detallado de cada política, incluyendo el nivel de cumplimiento de los equipos afectados, el top 5 de equipos que menos reglas cumplen, y el top 5 de las reglas más incumplidas dentro de cada política.", "inventoryPoliciesStatus": "Estado de políticas", "inventoryPoliciesHostsStatus": "Estado de hosts de políticas de inventario", "noInventory": "Sin inventario", "hostsAffected": "Hosts afectados", "hostsNotAffected": "Hosts no afectados", "levelCompliance": "<PERSON>vel de cumplimiento", "minComplianceLevel": "Percentil", "hostsComplyFewestRules": "Top 5 de hosts con menor cumplimiento de reglas", "nonApproved": "Reglas no aprobadas", "totalRules": "Total de reglas", "percentageCompliantRules": "% Reglas cumplidas", "unfulfilledRules": "Top 5 de reglas más incumplidas", "hostsPassed": "Hosts aprobados", "rule": "Regla", "ruleCondition": "Condición", "allowed": "Permitido", "notAllowed": "No permitido", "percentageHostsCompliant": "% Hosts cumplidos", "deploymentsSectionTitle": "Resumen de despliegues", "deploymentsReportIntroduction": "Esta sección contiene un resumen de todos los despliegues ejecutados mediante Batuta. Aquí podrá ver el total de equipos afectados en comparación con el total de hosts de la infraestructura, así como un listado de los 10 equipos más impactados por las acciones ejecutadas desde Batuta.", "deploymentsByOrigin": "Despliegues por origen", "origin": "Origen", "numberOfActions": "Nº de acciones", "responseActions": "Acciones de respuesta", "responseLibrary": "Biblioteca de respuestas", "deployments": "<PERSON><PERSON><PERSON><PERSON>", "reboots": "Re<PERSON><PERSON>s", "uninstalls": "Desinstalaciones", "hostsAffectedLast30Days": "Hosts afectados en los últimos 30 días", "affected": "Afectados", "nonAffected": "No afectados", "mostAffectedHosts": "Top 10 hosts más afectados", "hostName": "Equipo", "mostExecutedActions": "Top 5 acciones más ejecutadas", "actionName": "Nombre de la acción", "totalExecutions": "Total de ejecuciones", "success": "Éxito", "failed": "Fallido", "unknown": "Desconocida", "queued": "En cola", "cancel": "Cancelada", "actions": "Acciones", "actionsExecuted": "Acciones ejecutadas", "hosts": "Equipos", "host": "Equipo", "confidential": "Esta información es confidencial para ", "mail": "<EMAIL>", "web": "metabaseq.com", "type": "Tipo", "hostWithSecurityUpdates": "Equipos con actualizaciones de seguridad", "hostWithUpdates": "Equipos con actualizaciones", "hostUpToDate": "Equipos actualizados", "index": "Contenido", "levelOfProactivityTitle": "Nivel de proactividad", "levelOfProactivityIntroduction": "En esta sección, podrá conocer el nivel actual de proactividad de su compañía. Además, encontrará la distribución de los equipos según su nivel de proactividad y, por último, un desglose detallado por áreas acompañado de una tabla resumen con los valores.", "proactivitySummary": "Un análisis exhaustivo de sus distintas áreas ha revelado que su nivel de proactividad es del", "globalScore": "Puntaje global", "qualification": "Calificación", "percentage": "Po<PERSON>entaj<PERSON>", "proactivityHostDistribution": "Equipos por nivel de proactividad", "proactivityByArea": "Proactividad por área", "area": "Á<PERSON>", "osUpdates": "Actualizaciones de SO", "platform": "Cobertura de tecnología", "globalScoreDescription": "Realizando un cálculo detallado, tomando en consideración cada una de las áreas habilitadas y aplicando el peso respectivo a cada una, el puntaje general de su compañía es", "globalScoreDescription2": "con un porcentaje promedio de", "globalScoreDescription3": "en cada una de las áreas.", "High": "Alta", "Medium": "Media", "Low": "Baja", "total": "Total", "A": "A", "B": "B", "C": "C", "D": "D", "E": "E", "F": "F", "N/A": "N/A", "LOP.INTRO.TITLE": "Introducción al puntaje de nivel de proactividad", "LOP.INTRO.P1": "Este informe proporciona una evaluación integral de la postura de ciberseguridad de tu organización, centrándose en tu nivel de proactividad. La proactividad en ciberseguridad es esencial para mitigar riesgos y proteger información sensible. Involucra la identificación activa, abordaje y prevención de posibles amenazas antes de que puedan causar daños significativos.", "LOP.INTRO.P2": "Al analizar el desempeño de tu organización en áreas clave como cumplimiento, cobertura tecnológica y actualizaciones de SO, hemos calculado un puntaje de proactividad. Este puntaje sirve como un indicador valioso de la preparación general de seguridad de tu organización. Un puntaje de proactividad más alto significa una postura de seguridad más robusta, mientras que un puntaje más bajo indica áreas que requieren atención inmediata.", "LOP.INTRO.P3": "Comprender tu nivel de proactividad es crucial para tomar decisiones informadas y priorizar iniciativas de seguridad. Este informe proporcionará información sobre las fortalezas, debilidades y áreas de mejora de tu organización. Al tomar medidas proactivas para abordar los problemas identificados, puedes mejorar tu postura de seguridad y proteger tu organización de amenazas cibernéticas.", "LOP.KEY_TERMS.TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> clave", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.TITLE": "Nivel de proactividad", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.DESCRIPTION": "Métrica que evalúa el enfoque proactivo de una organización en ciberseguridad.", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.BP1": "Se calcula en función de factores como el cumplimiento de políticas de seguridad, el despliegue de tecnologías de seguridad y la aplicación oportuna de actualizaciones.", "LOP.KEY_TERMS.PROACTIVITY_LEVEL.BP2": "Un nivel de proactividad más alto indica una postura de seguridad más fuerte y un riesgo reducido", "LOP.KEY_TERMS.PROACTIVITY_SCORE.TITLE": "Puntaje de proactividad", "LOP.KEY_TERMS.PROACTIVITY_SCORE.DESCRIPTION": "Representación numérica del nivel de proactividad, generalmente expresada como un porcentaje.", "LOP.KEY_TERMS.PROACTIVITY_SCORE.BP1": "Se deriva de la evaluación de varias métricas y referencias de ciberseguridad.", "LOP.KEY_TERMS.PROACTIVITY_SCORE.BP2": "Un puntaje más alto significa un enfoque más proactivo en ciberseguridad.", "LOP.KEY_TERMS.GRADE_RANGES.TITLE": "Rangos de calificación", "LOP.KEY_TERMS.GRADE_RANGES.DESCRIPTION": "Categorización de puntajes de proactividad en calificaciones específicas (por ejemplo, A, B, C, D, E).", "LOP.KEY_TERMS.GRADE_RANGES.BP1": "Cada rango de calificación corresponde a un nivel particular de proactividad y nivel de riesgo asociado.", "LOP.KEY_TERMS.GRADE_RANGES.BP2": "Por ejemplo, una calificación de \"A\" indica una excelente proactividad, mientras que una calificación de \"E\" significa problemas críticos que requieren atención inmediata.", "LOP.KEY_TERMS.HOST.TITLE": "Host", "LOP.KEY_TERMS.HOST.DESCRIPTION": "Dispositivo conectado a una red, como una computadora o servidor.", "LOP.KEY_TERMS.CIS_CONTROLS.TITLE": "Controles CIS", "LOP.KEY_TERMS.CIS_CONTROLS.DESCRIPTION": "Conjunto de mejores prácticas y referencias de ciberseguridad desarrolladas por el Center for Internet Security (CIS). Estos controles proporcionan un marco para que las organizaciones mejoren su postura de seguridad.", "LOP.GLOBAL_SCORE.TITLE": "Puntaje global", "LOP.GLOBAL_SCORE.P1": "Basado en los datos recopilados por Batuta a través de los {{totalNumber}} agentes en tres áreas de evaluación (Cumplimiento, Cobertura Tecnológica y Actualizaciones de SO), el puntaje global de tu empresa es {{grade}} con un puntaje promedio de {{avgScore}}% en las tres áreas.", "OS_UPDATE.TITLE": "Actualizaciones del SO", "OS_UPDATE.DESCRIPTION": "Esta sección proporciona un resumen del estado de actualización de su infraestructura, incluyendo el número de actualizaciones pendientes (sistema operativo y seguridad) y una clasificación de equipos según si las actualizaciones están completas, pendientes o relacionadas con la seguridad.", "OS_UPDATE.KEY_FINDINGS": "Hallazgos clave", "OS_UPDATE.KEY_FINDINGS.BP1": "{{percentageHostsWithUpdates}}% de los equipos tienen actualizaciones de SO pendientes y {{percentageHostsWithSecurityUpdates}}% de los equipos tienen actualizaciones de seguridad pendientes", "OS_UPDATE.KEY_FINDINGS.HORIZONTAL_BAR_CHART.TITLE": "Distribución de actualizaciones de SO por SO", "OS_UPDATE.KEY_FINDINGS.HORIZONTAL_BAR_CHART.NUM_UPDATES": "Número de actualizaciones", "OS_UPDATE.KEY_FINDINGS.BP2": "Hay {{PERCENTAGE_UPDATES_OVER_30_DAYS}}% de actualizaciones de SO o actualizaciones de seguridad pendientes por más de 30 días", "OS_UPDATE.RECOMMENDED_ACTIONS": "Acciones recomendadas", "OS_UPDATE.RECOMMENDED_ACTIONS.BP1": "Asegúrese de que todas las actualizaciones de seguridad se apliquen rápidamente para reducir vulnerabilidades", "OS_UPDATE.RECOMMENDED_ACTIONS.BP2": "Establezca un calendario regular de actualizaciones para mantener la consistencia.", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.TITLE": "Actualizaciones de SO y seguridad pendientes por antigüedad", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.X.AXIS.DAYS": "Días", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.X.TITLE.TIME_PERIOD": "<PERSON><PERSON><PERSON> de tiempo (Días sin actualizar)", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.Y.TITLE.NUMBER_OF_UPDATES": "Número de actualizaciones", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.LABEL.OS_UPDATES": "Actualizaciones de SO", "OS_UPDATE.KEY_FINDINGS.DOUBLE_BAR_CHART.LABEL.SECURITY_UPDATES": "Actualizaciones de seguridad", "COMPLIANCE": "Cumplimiento", "TECHNOLOGY_COVERAGE": "Cobertura tecnológica", "OS_UPDATES": "Actualizaciones de SO", "SUMMARY.TITLE": "Resumen de hallazgos", "SUMMARY.GLOBAL_SCORE.TITLE": "Puntaje global", "SUMMARY.GLOBAL_SCORE.P1": "Basado en los datos recopilados por Batuta a través de los {{hostsCount}} agentes en tres áreas de evaluación (Cumplimiento, Cobertura Tecnológica y Actualizaciones de SO), el puntaje global de tu empresa es {{qualification}} con un puntaje promedio de {{score}}% en las tres áreas.", "SUMMARY.GLOBAL_SCORE.A": "El puntaje de proactividad global actual de A indica un alto nivel de preparación. Tu empresa demuestra buenas prácticas en las áreas de Cumplimiento, Cobertura Tecnológica y Actualizaciones de SO.", "SUMMARY.GLOBAL_SCORE.B": "El puntaje de proactividad global actual de B indica una base sólida en preparación para la ciberseguridad. Si bien algunas áreas aún requieren mejoras, la organización demuestra una base sólida en prácticas de ciberseguridad.", "SUMMARY.GLOBAL_SCORE.C": "El puntaje de proactividad global actual de C indica una preparación moderada. Si bien algunas áreas demuestran buenas prácticas, otras requieren atención inmediata para mejorar la postura de ciberseguridad de la organización.", "SUMMARY.GLOBAL_SCORE.D": "El puntaje de proactividad global actual de D indica un nivel de preparación limitado. Si bien algunas áreas demuestran prácticas básicas, existen brechas significativas en áreas críticas como los controles CIS de Windows, la cobertura tecnológica y las actualizaciones de SO. Se requiere atención inmediata para abordar estas vulnerabilidades y mejorar la postura de ciberseguridad de la organización.", "SUMMARY.GLOBAL_SCORE.E": "El puntaje de proactividad global actual de E indica un nivel mínimo de preparación. Existen deficiencias significativas en múltiples áreas, incluyendo los controles CIS de Windows, la cobertura tecnológica y las actualizaciones de SO. Se requieren acciones inmediatas y sustanciales para abordar estas vulnerabilidades críticas y mejorar la postura de ciberseguridad de la organización.", "SUMMARY.GLOBAL_SCORE.F": "Debido a la falta de datos reportados sobre los sistemas de la organización, no es posible realizar una evaluación integral de su postura de ciberseguridad en este momento. Una evaluación exhaustiva requiere datos precisos y oportunos sobre áreas clave como los controles CIS de Windows, la cobertura tecnológica y las actualizaciones de SO.", "SUMMARY.GLOBAL_SCORE.N/A": "Debido a compatibilidad, no es posible realizar una evaluación integral de la postura de ciberseguridad de tu organización en este momento.", "SUMMARY.KEY_FINDINGS.TITLE": "Hallazgos clave", "SUMMARY.KEY_FINDINGS.A.TITLE": "Fortalezas", "SUMMARY.KEY_FINDINGS.A.COMPLIANCE": "Calificación A ({{score}}%), indicando una adhesión excepcional a las políticas de seguridad, con problemas mínimos de incumplimiento, indicando un alto nivel de protección y cumplimiento con los controles CIS de Windows.", "SUMMARY.KEY_FINDINGS.A.TECHNOLOGY_COVERAGE": "Calificación A ({{score}}%), indicando una cobertura tecnológica excepcional, con todas las herramientas críticas de seguridad, incluyendo EDR, parcheo y gestión de vulnerabilidades, efectivamente implementadas y utilizadas activamente en todos los endpoints.", "SUMMARY.KEY_FINDINGS.A.OS_UPDATES": "Calificación A ({{score}}%), demostrando una gestión excepcional de actualizaciones del SO, con todos los dispositivos de la red consistentemente actualizados con el último sistema operativo y parches de seguridad.", "SUMMARY.KEY_FINDINGS.B.TITLE": "Fortalezas", "SUMMARY.KEY_FINDINGS.B.COMPLIANCE": "Calificación B ({{score}}%), indicando una base sólida en el cumplimiento de políticas de seguridad, con algunos problemas menores de incumplimiento que requieren atención, indicando una infraestructura bien protegida con margen de mejora.", "SUMMARY.KEY_FINDINGS.B.TECHNOLOGY_COVERAGE": "Calificación B ({{score}}%), demostrando una base sólida en cobertura tecnológica, con la mayoría de las herramientas críticas de seguridad, incluyendo EDR, parcheo y gestión de vulnerabilidades, efectivamente implementadas y utilizadas en la mayoría de los endpoints.", "SUMMARY.KEY_FINDINGS.B.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.C.TITLE": "<PERSON><PERSON><PERSON> mejora", "SUMMARY.KEY_FINDINGS.C.COMPLIANCE": "Calificación C ({{score}}%), indicando que existen algunas brechas de cumplimiento, particularmente en áreas de alto riesgo, indicando la necesidad de atención para mejorar la seguridad relacionada con los controles de seguridad.", "SUMMARY.KEY_FINDINGS.C.TECHNOLOGY_COVERAGE": "Calificación C ({{score}}%), indicando una cobertura tecnológica moderada, con algunas herramientas críticas de seguridad, como EDR, parcheo y gestión de vulnerabilidades, implementadas pero con brechas en la cobertura y utilización en los endpoints.", "SUMMARY.KEY_FINDINGS.C.OS_UPDATES": "Calificación C ({{score}}%), demostrando un nivel moderado de gestión de actualizaciones del SO, con algunos dispositivos con sistemas operativos desactualizados pero con actualizaciones de seguridad generalmente aplicadas.", "SUMMARY.KEY_FINDINGS.D.TITLE": "<PERSON><PERSON><PERSON> mejora", "SUMMARY.KEY_FINDINGS.D.COMPLIANCE": "Calificación D ({{score}}%), indicando brechas significativas de cumplimiento en áreas críticas relacionadas con los controles CIS de Windows. Se requiere acción urgente para mejorar el nivel de proactividad y mitigar los riesgos de seguridad asociados.", "SUMMARY.KEY_FINDINGS.D.TECHNOLOGY_COVERAGE": "Calificación D ({{score}}%), indicando una cobertura tecnológica limitada, con brechas significativas en la implementación y utilización de herramientas críticas de seguridad como EDR, parcheo y gestión de vulnerabilidades, dejando muchos endpoints vulnerables.", "SUMMARY.KEY_FINDINGS.D.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.E.TITLE": "<PERSON><PERSON><PERSON> mejora", "SUMMARY.KEY_FINDINGS.E.COMPLIANCE": "Calificación E ({{score}}%), indicando problemas graves de cumplimiento relacionados con los controles CIS de Windows. Esta situación crítica requiere una revisión y acción inmediata para mitigar los riesgos de seguridad asociados.", "SUMMARY.KEY_FINDINGS.E.TECHNOLOGY_COVERAGE": "Calificación E ({{score}}%), indicando una cobertura tecnológica mínima, con herramientas críticas de seguridad como EDR, parcheo y gestión de vulnerabilidades no implementadas o no utilizadas de manera efectiva, dejando la mayoría de los endpoints gravemente vulnerables.", "SUMMARY.KEY_FINDINGS.E.OS_UPDATES": "Calificación E ({{score}}%), indicando una falta crítica de gestión de actualizaciones del SO, con muchos dispositivos gravemente desactualizados y sin las actualizaciones de seguridad críticas, requiriendo atención inmediata.", "SUMMARY.KEY_FINDINGS.F.TITLE": "", "SUMMARY.KEY_FINDINGS.F.COMPLIANCE": "", "SUMMARY.KEY_FINDINGS.F.TECHNOLOGY_COVERAGE": "", "SUMMARY.KEY_FINDINGS.F.OS_UPDATES": "", "SUMMARY.KEY_FINDINGS.N/A.TITLE": "", "SUMMARY.KEY_FINDINGS.N/A.COMPLIANCE": "", "SUMMARY.KEY_FINDINGS.N/A.TECHNOLOGY_COVERAGE": "", "SUMMARY.KEY_FINDINGS.N/A.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.TITLE": "Acciones recomendadas", "SUMMARY.RECOMMENDED_ACTIONS.A.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.A.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.B.TITLE": "Acciones recomendadas", "SUMMARY.RECOMMENDED_ACTIONS.B.COMPLIANCE": "Analiza los tableros de Batuta en el módulo de Cumplimiento y ejecuta los scripts disponibles en la Biblioteca de Respuestas para solucionar problemas de incumplimiento relacionados con los controles CIS.", "SUMMARY.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "Utiliza Batuta para identificar rápidamente los dispositivos que carecen de herramientas críticas de ciberseguridad y usa el módulo de Nuevo Despliegue para completar la instalación en todos los dispositivos.", "SUMMARY.RECOMMENDED_ACTIONS.B.OS_UPDATES": "Utiliza Batuta para analizar las actualizaciones faltantes, identificar la causa raíz de cualquier actualización fallida y determinar los dispositivos que necesitan atención inmediata. Luego, utiliza tu tecnología de parcheo para desplegar los parches aprobados.", "SUMMARY.RECOMMENDED_ACTIONS.C.TITLE": "Acciones recomendadas", "SUMMARY.RECOMMENDED_ACTIONS.C.COMPLIANCE": "Analiza los tableros de Batuta en el módulo de Cumplimiento y ejecuta los scripts disponibles en la Biblioteca de Respuestas para solucionar problemas de incumplimiento relacionados con los controles CIS.", "SUMMARY.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "Utiliza Batuta para identificar rápidamente los dispositivos que carecen de herramientas críticas de ciberseguridad y usa el módulo de Nuevo Despliegue para completar la instalación en todos los dispositivos.", "SUMMARY.RECOMMENDED_ACTIONS.C.OS_UPDATES": "Utiliza Batuta para analizar las actualizaciones faltantes, identificar la causa raíz de cualquier actualización fallida y determinar los dispositivos que necesitan atención inmediata. Luego, utiliza tu tecnología de parcheo para desplegar los parches aprobados.", "SUMMARY.RECOMMENDED_ACTIONS.D.TITLE": "Acciones recomendadas", "SUMMARY.RECOMMENDED_ACTIONS.D.COMPLIANCE": "Analiza los tableros de Batuta en el módulo de Cumplimiento y ejecuta los scripts disponibles en la Biblioteca de Respuestas para solucionar problemas de incumplimiento relacionados con los controles CIS.", "SUMMARY.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "Utiliza Batuta para identificar rápidamente los dispositivos que carecen de herramientas críticas de ciberseguridad y usa el módulo de Nuevo Despliegue para completar la instalación en todos los dispositivos.", "SUMMARY.RECOMMENDED_ACTIONS.D.OS_UPDATES": "Utiliza Batuta para analizar las actualizaciones faltantes, identificar la causa raíz de cualquier actualización fallida y determinar los dispositivos que necesitan atención inmediata. Luego, utiliza tu tecnología de parcheo para desplegar los parches aprobados.", "SUMMARY.RECOMMENDED_ACTIONS.E.TITLE": "Acciones recomendadas", "SUMMARY.RECOMMENDED_ACTIONS.E.COMPLIANCE": "Analiza los tableros de Batuta en el módulo de Cumplimiento y ejecuta los scripts disponibles en la Biblioteca de Respuestas para solucionar problemas de incumplimiento relacionados con los controles CIS.", "SUMMARY.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "Utiliza Batuta para identificar rápidamente los dispositivos que carecen de herramientas críticas de ciberseguridad y usa el módulo de Nuevo Despliegue para completar la instalación en todos los dispositivos.", "SUMMARY.RECOMMENDED_ACTIONS.E.OS_UPDATES": "Utiliza Batuta para analizar las actualizaciones faltantes, identificar la causa raíz de cualquier actualización fallida y determinar los dispositivos que necesitan atención inmediata. Luego, utiliza tu tecnología de parcheo para desplegar los parches aprobados.", "SUMMARY.RECOMMENDED_ACTIONS.F.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.F.OS_UPDATES": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.TITLE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "", "SUMMARY.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "", "HOSTS_BY_PROACTIVITY.TITLE": "Hosts por nivel de proactividad", "HOSTS_BY_PROACTIVITY.P1": "La descomposición de hosts por nivel de proactividad se muestra a continuación. Actualmente, {{okayHostsPercent}}% de hosts ({{okayHostsCount}} de {{totalHosts}}) están dentro de un nivel de proactividad aceptable con una calificación de A o B. El {{hostsNeedAttention}}% restante de hosts (con calificaciones de C a E) necesita atención inmediata.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.TITLE": "Acciones recomendadas", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.COMPLIANCE": "", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "Fortalece tu postura de controles de seguridad creando un plan para alcanzar el 100% de cumplimiento con las políticas de seguridad internas y de CIS. Utiliza los scripts disponibles en la Biblioteca de Respuestas de Batuta para una remediación sencilla.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "Despliega y activa completamente las tecnologías con licencia para mejorar la cobertura, particularmente en áreas como EDR y anti-ransomware.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.A.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.B.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.C.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.D.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.E.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.F.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "HOSTS_BY_PROACTIVITY.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "Prioriza los esfuerzos de remediación en el {{highRiskPercentage}}% de hosts de alto riesgo clasificados como D y E, asegurando que todos estén actualizados y cumplan.", "PROACTIVITY_BY_AREA.TITLE": "Proactividad por área de evaluación", "PROACTIVITY_BY_AREA.P1": "Esta sección proporciona una evaluación detallada de la postura de ciberseguridad de tu organización basada en el nivel de proactividad de cada área de evaluación: Cumplimiento, Cobertura Tecnológica y Actualizaciones de SO.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.TITLE": "Hallazgos clave", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.COMPLIANCE": "Tu organización demuestra una adhesión excepcional a las políticas de seguridad, con problemas mínimos de incumplimiento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.COMPLIANCE": "Tu organización demuestra una base sólida en el cumplimiento de políticas de seguridad, con algunos problemas menores de incumplimiento que requieren atención.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.COMPLIANCE": "Tu organización tiene algunas brechas de cumplimiento, particularmente en áreas de alto riesgo. Abordar estos problemas es crucial para mejorar la postura de seguridad de tu organización.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.COMPLIANCE": "Tu organización tiene brechas significativas de cumplimiento, particularmente en áreas críticas como los controles CIS. Se requiere acción inmediata para abordar estos problemas.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.COMPLIANCE": "Tu organización tiene problemas graves de cumplimiento, lo que supone riesgos significativos para la postura de seguridad de tu organización. Se requiere acción inmediata y exhaustiva para abordar estos problemas.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.COMPLIANCE": "Debido a datos insuficientes, no es posible realizar una evaluación integral de la postura de cumplimiento de tu organización en este momento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.COMPLIANCE": "Debido a compatibilidad, no es posible realizar una evaluación integral de la postura de cumplimiento de tu organización en este momento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.TECHNOLOGY_COVERAGE": "Tu organización ha implementado y utilizado eficazmente las herramientas clave de ciberseguridad en todos los hosts, asegurando una protección integral.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.TECHNOLOGY_COVERAGE": "Tu organización ha implementado herramientas clave de ciberseguridad en una parte significativa de los hosts, pero hay oportunidades para mejorar la cobertura y la utilización.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.TECHNOLOGY_COVERAGE": "Tu organización ha implementado algunas herramientas de ciberseguridad, pero hay brechas significativas en la cobertura, de<PERSON><PERSON> much<PERSON> hosts desprotegidos.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.TECHNOLOGY_COVERAGE": "Tu organización tiene una implementación limitada de herramientas de ciberseguridad, de<PERSON><PERSON> hosts vulnerables a ataques.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.TECHNOLOGY_COVERAGE": "Tu organización tiene una implementación mínima de herramientas de ciberseguridad, dejando la mayoría de los hosts desprotegidos. Se necesita una acción urgente para mejorar la cobertura y proteger a tu organización.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.TECHNOLOGY_COVERAGE": "Debido a datos insuficientes, no es posible realizar una evaluación integral de la cobertura tecnológica de tu organización en este momento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.TECHNOLOGY_COVERAGE": "Debido a compatibilidad, no es posible realizar una evaluación integral de la cobertura tecnológica de tu organización en este momento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.A.OS_UPDATES": "Tu organización mantiene un fuerte proceso de gestión de actualizaciones, con todos los hosts actualizados con el último SO y parches de seguridad.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.B.OS_UPDATES": "Tu organización tiene un proceso de gestión de actualizaciones generalmente efectivo, pero algunos hosts pueden tener SO o parches de seguridad desactualizados.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.C.OS_UPDATES": "Tu organización tiene un nivel moderado de gestión de actualizaciones, con algunos hosts con SO o parches de seguridad desactualizados.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.D.OS_UPDATES": "Tu organización tiene un pobre proceso de gestión de actualizaciones, con muchos hosts con SO o parches de seguridad desactualizados.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.E.OS_UPDATES": "Tu organización tiene una falta crítica de gestión de actualizaciones, con muchos hosts gravemente desactualizados y altamente vulnerables a ataques. Es necesaria una acción inmediata para abordar estas vulnerabilidades.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.F.OS_UPDATES": "Debido a datos insuficientes, no es posible realizar una evaluación integral del estado de las actualizaciones de SO y seguridad de tu organización en este momento.", "PROACTIVITY_BY_AREA.KEY_FINDINGS.N/A.OS_UPDATES": "Debido a compatibilidad, no es posible realizar una evaluación integral del estado de las actualizaciones de SO y seguridad de tu organización en este momento.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.TITLE": "Acciones recomendadas", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.COMPLIANCE": "Los hosts no conformes deben ser analizados y corregidos lo antes posible utilizando los scripts pre-cargados de Batuta que se encuentran en la Biblioteca de Respuestas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.COMPLIANCE": "Los hosts no conformes deben ser analizados y corregidos lo antes posible utilizando los scripts pre-cargados de Batuta que se encuentran en la Biblioteca de Respuestas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.COMPLIANCE": "Los hosts no conformes deben ser analizados y corregidos lo antes posible utilizando los scripts pre-cargados de Batuta que se encuentran en la Biblioteca de Respuestas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.COMPLIANCE": "Los hosts no conformes deben ser analizados y corregidos lo antes posible utilizando los scripts pre-cargados de Batuta que se encuentran en la Biblioteca de Respuestas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.COMPLIANCE": "Los hosts no conformes deben ser analizados y corregidos lo antes posible utilizando los scripts pre-cargados de Batuta que se encuentran en la Biblioteca de Respuestas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.COMPLIANCE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.COMPLIANCE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.TECHNOLOGY_COVERAGE": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.TECHNOLOGY_COVERAGE": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.TECHNOLOGY_COVERAGE": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.TECHNOLOGY_COVERAGE": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.TECHNOLOGY_COVERAGE": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.TECHNOLOGY_COVERAGE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.TECHNOLOGY_COVERAGE": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.A.OS_UPDATES": "Recomendamos analizar las actualizaciones faltantes y aplicar las actualizaciones adecuadas del SO y de seguridad en las máquinas identificadas.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.B.OS_UPDATES": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.C.OS_UPDATES": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.D.OS_UPDATES": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.E.OS_UPDATES": "Para fortalecer tu postura de ciberseguridad y aprovechar tu inversión en licencias, las herramientas de ciberseguridad preaprobadas deben ser desplegadas en todos los hosts.", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.F.OS_UPDATES": "", "PROACTIVITY_BY_AREA.RECOMMENDED_ACTIONS.N/A.OS_UPDATES": "", "DETAILED_FINDINGS": "Resultados detallados", "HOSTS_SECTION.TITLE": "Información de equipos", "HOSTS_SECTION.P1": "A continuación encontrará un resumen del estado actual de la infraestructura de su empresa. En esta sección encontrará el número total de equipos, los equipos añadidos en los últimos 30 días e información relativa a la distribución geográfica y del sistema operativo de los equipos.", "HOSTS_SECTION.BAR_CHART_TITLE": "Distribución de equipos por SO", "HOSTS_SECTION.BAR_CHART_NUMBER_OF_HOSTS": "Número de equipos", "APPENDIX.TITLE": "A<PERSON>én<PERSON>", "COMPLIANCE.KEY_FINDINGS.TITLE": "Hallazgos clave", "COMPLIANCE.KEY_FINDINGS.P1": "A continuación se listan las Políticas de Seguridad aplicadas y habilitadas a través de Batuta:", "COMPLIANCE.KEY_FINDINGS.P2": "Las siguientes políticas están habilitadas, pero aplicadas a grupos inválidos:", "SECURITY_POLICY_DETAILS": "Detalles de política de seguridad", "SOFTWARE_INVENTORY_POLICY_DETAILS": "Detalles de políticas de inventario de software", "TECHNOLOGY_COVERAGE.PERCENT_OF_ACTIVE_LICENSES": "{{techName}}: {{percentActive}}% de licencias activas.", "TECHNOLOGY_COVERAGE.HOSTS_NO_TECH": "Actualmente, {{percentNoTech}}% de los hosts son vulnerables a brechas de seguridad puesto que no tienen ninguna tecnología instalada y están activos, lo que los convierte en riesgos de brechas que pueden comprometer toda tu infraestructura. Recomendamos desplegar inmediatamente la tecnología faltante a todos los endpoints."}}