import { ShellType, ShellTypes, ShellTypesByKernel } from '../models';

// Validate ShellType value
export const validateShellType = (value: string): boolean =>
  ShellTypes.includes(value as ShellType);

// Get OS from shell type
export const getOSFromShellType = (value: ShellType): string | undefined => {
  for (const [key, shellTypes] of Object.entries(ShellTypesByKernel)) {
    if (shellTypes.includes(value)) {
      return key;
    }
  }
  return undefined;
};
