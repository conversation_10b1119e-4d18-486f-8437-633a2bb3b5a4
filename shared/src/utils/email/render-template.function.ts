import fs from 'fs';
import Maizzle from '@maizzle/framework';

import templates, {
  ApplicationPolicyValues,
  AuthCodeValues,
  AwarenessAssignationValues,
  BatutaReportValues,
  GenericDataValues,
  HalcyonReportValues,
  NewInvitationValues,
} from './templates';
import tailwind from './tailwind.config';
import { baseCSS } from './custom-css';
import { TLanguage } from '@shared/types/languages.types';

export type TemplateName = keyof typeof templates;

interface TemplateMap {
  authCode: AuthCodeValues;
  halcyonReport: HalcyonReportValues;
  applicationPolicy: ApplicationPolicyValues;
  batutaReport: BatutaReportValues;
  awarenessAssignation: AwarenessAssignationValues;
  newInvitation: NewInvitationValues;

  // Group the generic ones here:
  requestError: GenericDataValues;
  botError: GenericDataValues;
  tempsFullError: GenericDataValues;
}

export type TemplateValuesType<T extends TemplateName> = T extends keyof TemplateMap
  ? TemplateMap[T]
  : never;

export type TemplateRenderParams<T extends TemplateName> = {
  language?: TLanguage;
  templateName: T;
  templateValues: TemplateValuesType<T>;
};

/**
 * Renders the indicated Maizzle template with the indicated values
 *
 * @param {TemplateRenderParams<TemplateName>} options {language: TLanguage, template: TemplateName, values: TemplateValuesType<TemplateName>}
 *
 * @return rendered html
 */
export default function renderTemplate<T extends TemplateName>({
  language,
  templateName,
  templateValues,
}: TemplateRenderParams<T>) {
  return new Promise<string>((resolve, reject) => {
    let templateString = templates[templateName];

    if (!templateString) {
      reject(new Error('Failed to import required template!'));
    }

    // Determine the template base path
    const isContainerizedOrExistDist = fs.existsSync('/app/shared/dist/utils/email/layout.html');
    const templateBasePath = isContainerizedOrExistDist
      ? '/app/shared/dist/utils/email'
      : '/app/shared/src/utils/email';

    // Replace templateBasePath in the template string before processing
    templateString = templateString.replace(/\{\{templateBasePath\}\}/g, templateBasePath);

    Maizzle.render(templateString, {
      tailwind: {
        config: tailwind as any,
        css: baseCSS,
      },
      maizzle: {
        inlineCSS: {
          enabled: true,
        },
        prettify: {
          enabled: true,
        },
        removeUnusedCSS: {
          enabled: true,
        },
        locals: {
          language: language || 'en',
          ...templateValues,
        },
      },
    })
      .then((render: { html: string }) => {
        resolve(render.html);
      })
      .catch((error: any) => reject(error));
  });
}
