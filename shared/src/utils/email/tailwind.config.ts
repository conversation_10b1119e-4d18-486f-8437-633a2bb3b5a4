export default {
  content: ['src/shared/utils/email/**/*.ts', 'src/shared/utils/email/**/*.html'],
  theme: {
    screens: {
      sm: {
        max: '800px',
      },
    },
    extend: {
      colors: {
        transparent: 'transparent',
        /* dynamic colors */
        primary: {
          DEFAULT: '#3db065',
          dark: '#215e35',
          light: '#75bf82',
        },
        secondary: {
          DEFAULT: '#24a4a5',
          dark: '#32878e',
          light: '#3fbed1',
        },
        neutral: {
          100: '#3a3b3d',
          75: '#545456',
          50: '#747273',
          25: '#c7c5c6',
          0: '#f1f1f1',
        },
        back: '#ffffff',
        main: '#262729',
        /* static colors */
        green: {
          DEFAULT: '#3db065',
          100: '#e0eddc',
          200: '#bfdcbd',
          300: '#9dce9f',
          400: '#75bf82',
          600: '#215e35',
        },
        blue: {
          DEFAULT: '#24a4a5',
          dark: '#32878e',
          light: '#3fbed1',
        },
        gray: {
          100: '#3a3b3d',
          75: '#545456',
          50: '#747273',
          25: '#c7c5c6',
          0: '#f1f1f1',
        },
        black: '#262729',
        white: '#ffffff',

        danger: {
          100: '#fff5f5',
          200: '#ffded7',
          300: '#feb2b2',
          400: '#fc8181',
          500: '#f56565',
          600: '#e53e3e',
          700: '#c53030',
          800: '#9b2c2c',
          900: '#742a2a',
        },
        warning: {
          100: '#fffaf0',
          200: '#feebc8',
          300: '#fbd38d',
          400: '#f6ad55',
          500: '#ed8936',
          600: '#dd6b20',
          700: '#c05621',
          800: '#9c4221',
          900: '#7b341e',
        },
        info: {
          100: '#e6fffa',
          200: '#c9ffff',
          300: '#81e6d9',
          400: '#4fd1c5',
          500: '#38b2ac',
          600: '#319795',
          700: '#2c7a7b',
          800: '#285e61',
          900: '#234e52',
        },
        success: {
          100: '#f0fff4',
          200: '#dbfce8',
          300: '#9ae6b4',
          400: '#68d391',
          500: '#48bb78',
          600: '#38a169',
          700: '#2f855a',
          800: '#276749',
          900: '#22543d',
        },
      },
      spacing: {
        screen: '100vw',
        full: '100%',
        px: '1px',
        0: '0',
        2: '2px',
        4: '4px',
        6: '6px',
        8: '8px',
        10: '10px',
        12: '12px',
        14: '14px',
        16: '16px',
        20: '20px',
        24: '24px',
        28: '28px',
        32: '32px',
        36: '36px',
        40: '40px',
        44: '44px',
        48: '48px',
        56: '56px',
        64: '64px',
        180: '180px',
        200: '200px',
        250: '250px',
        300: '300px',
        400: '400px',
        500: '500px',
        600: '600px',
        800: '800px',
        '1/2': '50%',
        '1/3': '33.333333%',
        '2/3': '66.666667%',
        '1/4': '25%',
        '2/4': '50%',
        '3/4': '75%',
        '1/5': '20%',
        '2/5': '40%',
        '3/5': '60%',
        '4/5': '80%',
        '1/6': '16.666667%',
        '2/6': '33.333333%',
        '3/6': '50%',
        '4/6': '66.666667%',
        '5/6': '83.333333%',
        '1/12': '8.333333%',
        '2/12': '16.666667%',
        '3/12': '25%',
        '4/12': '33.333333%',
        '5/12': '41.666667%',
        '6/12': '50%',
        '7/12': '58.333333%',
        '8/12': '66.666667%',
        '9/12': '75%',
        '10/12': '83.333333%',
        '11/12': '91.666667%',
      },
      borderRadius: {
        sm: '2px',
        default: '4px',
        lg: '8px',
      },
      fontFamily: {
        sans: ['Roboto', 'Oxygen', '-apple-system', 'BlinkMacSystemFont', 'Arial', 'sans-serif'],
        mono: ['source-code-pro', 'Menlo', 'Monaco', 'Consolas', 'Courier', 'monospace'],
      },
      fontSize: {
        0: '0',
        xs: '12px',
        sm: '14px',
        base: '16px',
        lg: '18px',
        xl: '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
      },
      inset: (theme: any) => ({
        ...theme('spacing'),
      }),
      letterSpacing: {
        tighter: '-2px',
        tight: '-1px',
        normal: '0',
        wide: '1px',
        wider: '2px',
        widest: '4px',
      },
      lineHeight: (theme: any) => ({
        ...theme('spacing'),
      }),
      maxHeight: (theme: any) => ({
        ...theme('spacing'),
      }),
      maxWidth: (theme: any) => ({
        ...theme('spacing'),
      }),
      minHeight: (theme: any) => ({
        ...theme('spacing'),
      }),
      minWidth: (theme: any) => ({
        ...theme('spacing'),
      }),
    },
  },
  variants: {},
  corePlugins: {
    animation: false,
    backgroundOpacity: false,
    borderOpacity: false,
    divideOpacity: false,
    placeholderOpacity: false,
    textOpacity: false,
  },
  plugins: [],
};
