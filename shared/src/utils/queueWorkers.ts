import { KAFKA_BROKERS } from '@shared/constants/env';
import { Logger } from '@shared/helpers/classes/logger.class';
import { KafkaService } from '@shared/helpers/classes/queue-worker.class';

let kafka: KafkaService;

export const initKafkaQueue = async () => {
  if (!KAFKA_BROKERS) {
    Logger.warning('Kafka environment variables not set, skipping consumer initialization.');
    return;
  }

  kafka = await KafkaService.getInstance();
  await kafka.connect();
  kafka.startConsuming();
};
