import { toNumber } from 'lodash';

export const sortAndPaginate = (
  data: Record<string, any>[],
  offset: number | string = 0,
  limit: number | string | null = 25,
  sortBy: string = 'name',
  sortOrder: string = 'asc'
) => {
  data.sort((a: Record<string, any>, b: Record<string, any>) => {
    const nameComparison = `${a[sortBy]}`.localeCompare(`${b[sortBy]}`);
    return sortOrder === 'asc' ? nameComparison : -nameComparison;
  });

  const startIndex = toNumber(offset);

  let endIndex = startIndex + data.length - 1;
  let paginatedData = data;
  if (toNumber(limit) > 0) {
    endIndex = startIndex + toNumber(limit);
    paginatedData = data.slice(startIndex, endIndex);
  }

  return {
    totalItems: data.length,
    currentOffset: toNumber(offset),
    currentLimit: toNumber(limit),
    data: paginatedData,
  };
};
