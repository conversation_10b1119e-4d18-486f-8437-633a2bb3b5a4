// Schemas
import { User } from '@shared/schemas';
// Helpers
import { Notifier } from '@shared/helpers/classes/notifier.class';

export const sendGeneralPush = async (
  title: string,
  contents: string,
  data: { [key: string]: any }
) => {
  // usuarios
  const users = (await User.find({})).map((user) => user.email);
  // envio de notificacion a todos los usuarios
  return Notifier.sendPushNotification({ to: users, title, contents, data });
};
