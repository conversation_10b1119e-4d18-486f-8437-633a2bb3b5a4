export const parseFiltersStringToObject = (
  filtersString: string,
  listFilters: string[] = []
): { [key: string]: { condition: string; value?: string; values?: string[] } } => {
  const filtersObject: { [key: string]: { condition: string; value?: string; values?: string[] } } =
    {};

  filtersString.split('|').forEach((filterString) => {
    const [field, condition, value] = filterString.split(':');

    if (field && condition) {
      if (filtersObject.hasOwnProperty(field)) {
        // Exists previously
        if (filtersObject[field].value) {
          const previousValue = filtersObject[field].value as string;
          filtersObject[field] = { condition, values: [previousValue, value] };
        } else (filtersObject[field].values as string[]).push(value);
      } else {
        // Is a new filter
        filtersObject[field] = { condition };

        if (listFilters.includes(field)) {
          filtersObject[field].values = [value];
        } else {
          filtersObject[field].value = value;
        }
      }
    }
  });

  return filtersObject;
};

const getDate = (dateString: string): number => {
  const [year, month, day] = dateString.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
  return date.getTime();
};

export function filterDataByFilterObject<T extends Record<string, any>>(
  data: Record<keyof T, any>[],
  filterString?: string,
  getValue: (item: T, field: keyof T) => T[keyof T] = (item: T, field: keyof T) => item[field],
  dateFields: string[] = []
): Record<string, any>[] {
  if (!filterString) return data;

  const filters: Record<string, any> = parseFiltersStringToObject(filterString, []);

  let filteredData: T[] = JSON.parse(JSON.stringify(data));

  for (const [field, filter] of Object.entries(filters)) {
    const getLowerValue = (item: T) => `${getValue(item, field)}`.toLowerCase();
    const expectedValue = filter.value.toLowerCase();

    switch (filter.condition) {
      case 'is':
        filteredData = filteredData.filter((item) => getLowerValue(item) === expectedValue);
        break;
      case 'is_not':
        filteredData = filteredData.filter((item) => getLowerValue(item) !== expectedValue);
        break;
      case 'contains':
        filteredData = filteredData.filter((item) => getLowerValue(item).includes(expectedValue));
        break;
      case 'contains_not':
        filteredData = filteredData.filter((item) => !getLowerValue(item).includes(expectedValue));
        break;
      default:
        break;
    }

    if (dateFields.includes(field)) {
      switch (filter.condition) {
        case 'gt':
          filteredData = filteredData.filter(
            (item) => getDate(getLowerValue(item).substring(0, 10)) > getDate(expectedValue)
          );
          break;
        case 'lt':
          filteredData = filteredData.filter(
            (item) => getDate(getLowerValue(item).substring(0, 10)) < getDate(expectedValue)
          );
          break;
        case 'gte':
          filteredData = filteredData.filter(
            (item) => getDate(getLowerValue(item).substring(0, 10)) >= getDate(expectedValue)
          );
          break;
        case 'lte':
          filteredData = filteredData.filter(
            (item) => getDate(getLowerValue(item).substring(0, 10)) <= getDate(expectedValue)
          );
          break;
        default:
          break;
      }
    } else {
      const getAndParseInt = (item: T) => parseInt(getLowerValue(item));

      switch (filter.condition) {
        case 'gt':
          filteredData = filteredData.filter(
            (item) => getAndParseInt(item) > parseInt(expectedValue)
          );
          break;
        case 'lt':
          filteredData = filteredData.filter(
            (item) => getAndParseInt(item) < parseInt(expectedValue)
          );
          break;
        case 'gte':
          filteredData = filteredData.filter(
            (item) => getAndParseInt(item) >= parseInt(expectedValue)
          );
          break;
        case 'lte':
          filteredData = filteredData.filter(
            (item) => getAndParseInt(item) <= parseInt(expectedValue)
          );
          break;
        default:
          break;
      }
    }
  }

  return filteredData;
}
