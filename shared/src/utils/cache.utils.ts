import { createHash } from 'crypto';

import NodeCache from 'node-cache';

const cache = new NodeCache();

export const useCache = async (
  getData: () => Promise<any>,
  cacheKey: string,
  ttl: number = 1 * 60
) => {
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }

  const data = await getData();
  if (ttl > 0) {
    cache.set(cacheKey, data, ttl);
  }

  return data;
};

export const generateCacheKey = (...params: any[]): string => {
  const keyString = params
    .map((param) => (typeof param === 'object' ? JSON.stringify(param) : param))
    .join('|');
  const hash = createHash('sha256').update(keyString).digest('hex');
  return hash;
};
