import { Functionality } from '@shared/schemas';
import { errors } from './app-errors';

export const getFunctionalityByNameOrThrow = async (name: string) => {
  const functionality = await Functionality.findOne({ name: name });
  if (!functionality) throw errors.not_found(`Functionality ${name}`);
  return functionality;
};

export const getFunctionalityByInternalNameOrThrow = async (name: string) => {
  const functionality = await Functionality.findOne({ internalName: name });
  if (!functionality) throw errors.not_found(`Functionality ${name}`);
  return functionality;
};
