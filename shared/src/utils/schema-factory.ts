import { Query, Schema, SchemaDefinition, SchemaOptions } from 'mongoose';

import { SchemaUtils } from '@shared/helpers/classes/schema-utils.class';
import { FilterModel } from '@shared/models';

import { errors } from './app-errors';

/*
The inferred type of 'default' cannot be named without a reference to 'mongoose/node_modules/mongodb'.
This is likely not portable. A type annotation is necessary.
*/

const SchemaFactory = (
  definition: SchemaDefinition,
  options: SchemaOptions = {},
  useDefaultFields = true
): Schema => {
  const _options = {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    minimize: false,
    ...options,
  };

  const schema = new Schema(
    {
      ...(useDefaultFields
        ? {
            deleted: {
              type: Boolean,
              default: false,
            },
            enabled: {
              type: Boolean,
              default: true,
            },
            protected: {
              type: Boolean,
              default: false,
            },
          }
        : {}),
      ...definition,
    },
    _options
  );

  if (_options.timestamps) {
    schema.index({ createdAt: 1 });
    schema.index({ updatedAt: 1 });
  }

  if (useDefaultFields) {
    schema.index({ deleted: 1 });
    schema.index({ enabled: 1 });
    schema.index({ protected: 1 });

    schema.pre(
      ['updateOne', 'updateMany', 'findOneAndUpdate'],
      async function (
        this: Query<any, any, {}, unknown, 'find' | 'update', Record<string, never>> & {
          options?: { bypassProtection?: boolean };
        },
        next
      ) {
        if (this.options?.bypassProtection) {
          return next();
        }

        const docsToUpdate = await this.model.find(this.getFilter());
        // Validate protection
        if (docsToUpdate?.some((doc) => doc.protected)) {
          throw errors.protected_document();
        }
      }
    );

    schema.pre(
      ['deleteOne', 'deleteMany'],
      async function (
        this: Query<any, any, {}, unknown, 'delete', Record<string, never>> & {
          options?: { bypassProtection?: boolean };
        },
        next
      ) {
        if (this.options?.bypassProtection) {
          return next();
        }

        const docsToUpdate = await this.model.find(this.getFilter());
        // Validate protection
        if (docsToUpdate?.some((doc) => doc.protected)) {
          throw errors.protected_document();
        }
      }
    );
  }

  // Filter Manager
  schema.statics.createFilter = (extra?: FilterModel, skip?: string[], extraFields?: string[]) =>
    SchemaUtils.createFilter(schema, extra, skip, extraFields); // on skip we can add the fields of the schema that we doesn't want to include on the filters
  schema.statics.parseFilter = (filter?: string, sort?: string, extra?: string[]) =>
    SchemaUtils.parseFilterString(schema, filter, sort, extra);

  return schema;
};

export default SchemaFactory;
