import { PASSWORD_REGEX } from '@shared/constants/regex';

export const numbersChars = '0123456789';
export const specialChars = '.+-,()[]';
export const lowerChars = 'abcdefghijklmnopqrstuvwxyz';
export const upperChars = lowerChars.toUpperCase();
export const passwordChars = [numbersChars, lowerChars, upperChars, specialChars].join('');
export const noSpecialChars = [numbersChars, lowerChars, upperChars].join('');

export const randomString = ({ chars = noSpecialChars, length = 0, min = 8, max = 16 } = {}) => {
  let string = '';

  if (!length) {
    const maximal = Math.floor(Math.random() * max) + 1;
    length = maximal < min ? min : maximal;
  }

  for (let i = 0; i < length; i += 1) {
    const rnum = Math.floor(Math.random() * chars.length);
    string += chars.substring(rnum, rnum + 1);
  }

  return string;
};

export const randomPasswordRport = (): string => {
  return randomString({ chars: noSpecialChars, min: 12, max: 16 });
};

export const randomPassword = ({ min = 10, max = 20, attempt = 1 } = {}): string | null => {
  const password = randomString({ chars: passwordChars, min, max });

  // to prevent an infinite loop...
  if (attempt > 50) return null;

  return password.match(PASSWORD_REGEX)
    ? password
    : randomPassword({ min, max, attempt: attempt + 1 });
};

export const capitalize = (text: string) => {
  return text
    .trim()
    .toLowerCase()
    .replace(/\w\S*/g, (w: string) => w.replace(/^\w/, (c: string) => c.toUpperCase()));
};

export function toPercentage(decimalNumber: number, fractionDigits: number = 2) {
  if (!decimalNumber || isNaN(decimalNumber)) return '0';
  return (decimalNumber * 100).toFixed(fractionDigits).replace(/\.?0+$/, '');
}
