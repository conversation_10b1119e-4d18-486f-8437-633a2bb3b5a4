import { rateLimit } from 'express-rate-limit';
import { Request, Response } from 'express';

export const createRateLimiter = (windowMs: number, limit: number) => {
  return rateLimit({
    windowMs,
    limit,
    standardHeaders: true,
    legacyHeaders: false,
    handler: (_: Request, res: Response) => {
      res.status(429).json({
        code: 'TOO_MANY_REQUESTS',
        message: `Too many requests, please try again in ${Math.ceil(windowMs / 1000)} seconds.`,
      });
    },
  });
};
