import { Request } from 'express';
import { SchemaBasicModel, SchemaBasicStatics, SortQueryModel } from '../models';
import { GetAllQuery } from '../types';
import { Document, FilterQuery, Model, PopulateOptions } from 'mongoose';

/**
 * Utility function that parses the given search query and returns the needed
 * objects to perform the operation and return a paginated result
 * @param req The express request object with the validated params. (Must be
 * validated since validation passes the query elements to the body property)
 * @param model The Mongoose Model to perform the find operation with.
 * @returns A Promise that resolves to a list containing two elements: The
 * requested page of items to return, and the total number of objects that match the filter.
 */
export default async function paginateAndFindFiltered<T extends SchemaBasicModel>(
  req: Request | Record<string, any>,
  model: SchemaBasicStatics & Model<T, {}, {}, {}, Document<unknown, {}, T> & T>,
  options?: { populate?: PopulateOptions }
) {
  const { filter, limit = 100, offset = 0, sort } = req.query as GetAllQuery;

  // Building the query based on the constructed filters
  const [query, sortQuery] = model.parseFilter(filter, sort) as [
    FilterQuery<T>,
    SortQueryModel | undefined,
    {
      [key: string]: any;
    },
  ];
  let rowsQuery = model
    .find(query)
    .sort(sortQuery ?? '-createdAt')
    .limit(limit ?? 100)
    .skip(offset ?? 0);

  if (options?.populate) {
    rowsQuery = rowsQuery.populate(options.populate);
  }

  return await Promise.all([await rowsQuery.exec(), await model.countDocuments(query)] as const);
}
