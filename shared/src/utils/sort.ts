export const parseSortString = (sort: string = ''): Record<string, string> => {
  const [field, orderNumber] = sort.split(':');
  const order = orderNumber === '-1' ? 'desc' : 'asc';
  return { order, field };
};

export const sortByField = (
  data: Record<string, any>[],
  sort: string = ''
): Record<string, any>[] => {
  const { order, field } = parseSortString(sort);

  return data.sort((a: Record<string, any>, b: Record<string, any>) => {
    const nameComparison = `${a[field]}`.localeCompare(`${b[field]}`);
    return order === 'asc' ? nameComparison : -nameComparison;
  });
};
