import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { HalcyonReportEmailModel } from '../helpers/models';

const halcyonReportEmailSchema = schemaFactory({
  email: { type: String, required: true },
});

export type HalcyonReportEmailDocument =
  | (HalcyonReportEmailModel & Document & { _id: Types.ObjectId })
  | null;
export const HalcyonReportEmail = model<HalcyonReportEmailModel>(
  'HalcyonReportEmail',
  halcyonReportEmailSchema
);
