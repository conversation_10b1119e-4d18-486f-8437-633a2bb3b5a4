import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { HalcyonAccessKeys } from '../helpers/models';

const halcyonAccessKeysSchema = schemaFactory({
  accessToken: { type: String, required: true, default: '' },
  refreshToken: { type: String, required: true, default: '' },
});

export type HalcyonAccessKeyDocument =
  | (HalcyonAccessKeys & Document & { _id: Types.ObjectId })
  | null;
export const HalcyonAccessKey = model<HalcyonAccessKeys>(
  'HalcyonAccessKeys',
  halcyonAccessKeysSchema
);
