import { AxiosError } from 'axios';
import { errors, generateError } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

// This function handles halcyon errors in responses
export const handleHalcyonErrors = (error: AxiosError) => {
  Logger.error(`Halcyon API error: ${error}`);

  if (!error.isAxiosError)
    throw generateError('Error communicating with Halcyon API', 500, 'HALCYON_ERROR');

  const response = error.response as {
    data: { status?: string; message?: string; code?: number; errors: object | string[] };
    status?: number;
  };

  if (!response?.data)
    throw generateError('Error communicating with Halcyon API', 500, 'HALCYON_ERROR');

  if (response.status === 401) throw errors.halcyon_authentication_failed();

  Logger.error('Halcyon error detail:', response.data);

  throw generateError(
    Array.isArray(response.data.errors)
      ? response.data.errors[0]
      : (response.data.message ?? 'Error communicating with Halcyon API'),
    response.status || 500,
    'HALCYON_ERROR'
  );
};
