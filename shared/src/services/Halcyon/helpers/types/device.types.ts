import { BasicHalcyonBody, PaginatedResponseBody } from './default.types';

export type HalcyonDevice = {
  id: string;
  name: string;
  os_name: string;
  registered_date: string;
  heartbeat: string;
  agent_version: string;
};

export type DevicesResponse = PaginatedResponseBody & {
  data: HalcyonDevice[];
};

export type DeviceResponse = BasicHalcyonBody & {
  data: {
    id: string;
    name: string;
    local_name: string;
    registered_date: string;
    heartbeat: string;
    epp_list: any[];
    agent_info: {
      id: string;
      name: string;
      version: string;
      last_updated: string;
    };
    system_info: {
      id: string;
      boot_time: string;
      manufacturer: string;
      model: string;
      type: string;
      processor: string;
      bios_version: string;
      boot_device: string;
      system_locale: string;
      input_locale: string;
      timezone: string;
      total_physical_memory: string;
      recorded_time: string;
      os_install_id: string;
      os_install: {
        id: string;
        registered_owner: string;
        registered_org: string;
        product_id: string;
        install_date: string;
        domain: string;
        logon_server: string;
        hotfixes: string;
        os_id: string;
        os: {
          id: string;
          name: string;
          version: string;
          manufacturer: string;
          configuration: string;
          build_type: string;
        };
      };
    };
  };
};

export type HalcyonDeviceMetrics = BasicHalcyonBody & {
  data: {
    total: number;
    no_heartbeats: number;
    need_updates: number;
  };
};
