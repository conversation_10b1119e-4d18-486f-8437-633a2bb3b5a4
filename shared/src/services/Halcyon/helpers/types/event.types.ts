import { BasicHalcyonBody, PaginatedResponseBody } from './default.types';

type HalcyonEvent = {
  id: string;
  tenant_id: string;
  device_id: string;
  device_name: string;
  name: string;
  action_type: string;
  threat_id: string;
  executable_path: string;
  executing_user: string;
  occurrence_date: string;
  deleted_at: null | Date;
  type: string;
  action: {
    type: string;
    occurrence_date: Date;
  };
};

export type EventResponse = BasicHalcyonBody & {
  data: HalcyonEvent;
};

export type EventsResponse = PaginatedResponseBody & {
  data: HalcyonEvent[];
};

type HalcyonMetrics = {
  total: number;
  blocked: number;
  allowed: number;
  sandboxed: number;
  total_duplicates: number;
  blocked_duplicates: number;
  allowed_duplicates: number;
  sandboxed_duplicates: number;
};

export type EventMetricsResponse = BasicHalcyonBody & {
  data: HalcyonMetrics;
};

export type MonthMetricResponse = BasicHalcyonBody & {
  months: { [key: string]: HalcyonMetrics };
};

export type PartitionedMetricsResponse = BasicHalcyonBody & {
  partition_type: string;
  partitions: {
    [key: string]: HalcyonMetrics;
  };
};
