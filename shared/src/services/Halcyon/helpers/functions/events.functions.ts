import { getCriticalHalcyonEvents, getHalcyonEvents } from '../connections';

export const getEvents = async (start_time: string, end_time: string) => {
  let events = await getHalcyonEvents(undefined, undefined, 100, start_time, end_time);
  let data = [...events.data];
  while (events.pagination.next_page) {
    events = await getHalcyonEvents(undefined, events.pagination.next_page, 100);
    data = [...data, ...events.data];
  }
  return data;
};

export const getCriticalEvents = async (start_time: string, end_time: string) => {
  let events = await getCriticalHalcyonEvents(undefined, undefined, 100, start_time, end_time);
  let data = [...events.data];
  while (events.pagination.next_page) {
    events = await getCriticalHalcyonEvents(undefined, events.pagination.next_page, 100);
    data = [...data, ...events.data];
  }
  return data;
};
