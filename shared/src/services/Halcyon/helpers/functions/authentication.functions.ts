import { HalcyonAccessKey } from '../../schemas/';
import { getAccessToken } from '../connections/authentication';

// Getting the accessKeys saved in mongo
export const getAccessKeys = async () => {
  // getAccessKeys from mongo
  const halcyonAccessKeys = await HalcyonAccessKey.find({});
  if (halcyonAccessKeys.length === 0) {
    try {
      // getting accessKey
      const newAccessKeys = await getAccessToken();
      // creating document
      await HalcyonAccessKey.create({
        accessToken: newAccessKeys.accessToken,
        refreshToken: newAccessKeys.refreshToken,
      });
      // returning new accessKey
      return newAccessKeys.accessToken;
    } catch (err) {
      throw err;
    }
  }
  // return accessKey
  return halcyonAccessKeys[0].accessToken;
};

// Getting accessKeys from Halcyon API
export const refreshAccessKeys = async () => {
  // getAccessKeys from mongo
  const halcyonAccessKeys = await HalcyonAccessKey.find({});
  if (halcyonAccessKeys.length === 0) {
    return await getAccessKeys();
  }
  // getAccessKeys from Halcyon
  try {
    // getting accessKey
    const newAccessKeys = await getAccessToken();
    await HalcyonAccessKey.findOneAndUpdate(halcyonAccessKeys[0]._id, {
      accessToken: newAccessKeys.accessToken,
      refreshToken: newAccessKeys.refreshToken,
    });
    return newAccessKeys.accessToken;
  } catch (err) {
    throw err;
  }
};

// Returning accessKeys for RportPlatform
export const getHalcyonToken = async (username: string, password: string) => {
  try {
    // getting accessKey
    const newAccessKeys = await getAccessToken(username, password);
    return newAccessKeys.accessToken;
  } catch (err) {
    throw err;
  }
};
