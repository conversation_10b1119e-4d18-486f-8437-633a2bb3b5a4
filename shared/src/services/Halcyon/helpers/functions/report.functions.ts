import _ from 'lodash';

import { Time } from '@shared/helpers/classes/times.class';

import { HalcyonDevice } from '../types/';

import { getCriticalHalcyonEvents, getDevices, getHalcyonEventMetrics } from '../connections';

import { getCriticalEvents, getEvents, getReportEmails } from './';
import { Notifier } from '@shared/helpers/classes/notifier.class';

export const getEventData = async (start_time: string, end_time: string) => {
  // Events info
  const [total, allowed, blocked] = await getMonthlyEvents(start_time, end_time);
  const critical = await getCriticalEventsCount(start_time, end_time);
  return { total, blocked, allowed, critical };
};
export const getDeviceData = async (start_time: string, end_time: string) => {
  // getting all the devices
  const devices = await getDeviceList();
  // getting heartbeat
  const disconnectedDevices = getDisconnectedDevices(devices, start_time);
  const newDevices = getNewDevices(devices, start_time, end_time);

  // Getting top devices with most alerts
  const mostAlertedOnes = await getMostAlertedDevices(start_time, end_time);

  // // Getting top devices witm most critical alerts
  const mostCriticalOnes = await getMostCriticalDevice(start_time, end_time);

  return {
    total: devices.length,
    disconnectedOnes: disconnectedDevices.length,
    createdOnes: newDevices.length,
    top5Alerted: mostAlertedOnes,
    top5Critical: mostCriticalOnes,
  };
};

// Transforming Event Data
const getMonthlyEvents = async (start_time: string, end_time: string) => {
  const metrics = await getHalcyonEventMetrics(undefined, start_time, end_time);
  return [metrics.data.total, metrics.data.blocked, metrics.data.allowed];
};

const getCriticalEventsCount = async (start_time: string, end_time: string) => {
  const events = await getCriticalHalcyonEvents(
    undefined,
    undefined,
    undefined,
    start_time,
    end_time
  );
  return events.pagination.total;
};

// Transforming Device Data

const getDeviceList = async () => {
  // get total devices
  let devices = await getDevices(undefined, undefined, 100);
  let data = [...devices.data];
  while (devices.pagination.next_page) {
    devices = await getDevices(undefined, devices.pagination.next_page, 100);
    data = [...data, ...devices.data];
  }
  // get total of devices added in the period
  return data;
};

const getDisconnectedDevices = (devices: HalcyonDevice[], start_time: string) => {
  // get devices with not connection in that period REMEMBER TO SPECIFY IN THE REPORT THAT IF THESE DEVICES HAS A RECENT HEARTBEAT THE WONT APPEAR ON THE REPORT
  const start = new Date(start_time);

  return devices.filter((device) => !Time.isAfter(new Date(device.heartbeat), start));
};

const getNewDevices = (devices: HalcyonDevice[], start_time: string, end_time: string) => {
  // get devices that need uptades same as heartbeat
  const end = new Date(end_time);
  const start = new Date(start_time);
  return devices.filter((device) => Time.isBetween(new Date(device.registered_date), start, end));
};

const getMostAlertedDevices = async (start_time: string, end_time: string) => {
  // get events in these period and group by name
  const events = await getEvents(start_time, end_time);

  const count = _.chain(events)
    .groupBy('device_name')
    .map((value, key) => ({
      name: key,
      count: value.length,
    }))
    .orderBy('count', 'desc')
    .value();

  return count;
};

const getMostCriticalDevice = async (start_time: string, end_time: string) => {
  // get critical events in these period and group by name
  const events = await getCriticalEvents(start_time, end_time);

  const count = _.chain(events)
    .groupBy('device_name')
    .map((value, key) => ({
      name: key,
      count: value.length,
    }))
    .orderBy('count', 'desc')
    .value();

  return count;
};

// Send report Funtcion
export const sendHalcyonReport = async (start_time: string, end_time: string) => {
  // Events Data
  const eventData = await getEventData(start_time, end_time);
  // Devices Data
  const deviceData = await getDeviceData(start_time, end_time);

  const emails = await getReportEmails();

  const month = Time.getMonthName(new Date(start_time).getMonth(), 'es');
  const year = new Date(start_time).getFullYear();

  // notifier
  Notifier.sendEmail({
    language: 'es',
    to: emails,
    subject: `Informe mensual de consola Halcyon - HumanApis - ${month} ${year}`,
    templateName: 'halcyonReport',
    templateValues: {
      startDate: new Date(start_time).toLocaleDateString(),
      endDate: new Date(end_time).toLocaleDateString(),
      newEvents: eventData.total,
      blockedEvents: eventData.blocked,
      allowedEvents: eventData.allowed,
      criticalEvents: eventData.critical,
      totalDevices: deviceData.total,
      newDevices: deviceData.createdOnes,
      disconnectedDevices: deviceData.disconnectedOnes,
      mostAlerted: deviceData.top5Alerted,
      mostCritical: deviceData.top5Critical,
    },
  });

  return {
    startDate: start_time,
    endDate: end_time,
    newEvents: eventData.total,
    blockedEvents: eventData.blocked,
    allowedEvents: eventData.allowed,
    criticalEvents: eventData.critical,
    totalDevices: deviceData.total,
    newDevices: deviceData.createdOnes,
    disconnectedDevices: deviceData.disconnectedOnes,
    mostAlerted: deviceData.top5Alerted,
    mostCritical: deviceData.top5Critical,
  };
};

export const sendMonthlyReport = async () => {
  const nextMonth = Time.getNextMonth(new Date());
  const [firstDay, lastDay] = Time.lastMonthDays(nextMonth);
  try {
    await sendHalcyonReport(firstDay.toISOString(), lastDay.toISOString());
  } catch (err) {
    throw err;
  }
  return 'Monthly Halcyon report has been sent';
};
