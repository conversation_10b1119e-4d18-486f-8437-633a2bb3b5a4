import { getDevice, getDevices, removeDevice } from '../connections/';

import { errors } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';
import { DevicesResponse, HalcyonDevice } from '../types';

export const getDeviceByName = async (deviceName: string, token?: string) => {
  const limit = 100;
  let page = 1;
  let continueSearch = true;
  let searchedDevice: HalcyonDevice | undefined;
  let response: DevicesResponse;

  while (continueSearch) {
    response = await getDevices(token, page, limit);

    // search by name
    searchedDevice = response.data.find(
      (device) => device.name.toLocaleLowerCase() === deviceName.toLocaleLowerCase()
    );

    if (searchedDevice) {
      return searchedDevice;
    } else if (!response.pagination.next_page) {
      continueSearch = false;
    }

    page++;
  }

  throw errors.not_found(`Halcyon device with name: ${deviceName}`);
};

export const getHalcyonAgentId = async (deviceName: string, token?: string) => {
  let agentId = '';

  try {
    const device = await getDeviceByName(deviceName, token);

    agentId = device.id;
  } catch (error) {
    Logger.error(`Error trying to get device info from Halcyon: ${error}`);
  }

  return agentId;
};

export const getHalcyonDeviceFromAgentId = async (agentId: string, token?: string) => {
  // get the device by its ID
  const response = await getDevice(agentId, token);

  if (!response.data) throw errors.not_found(`Halcyon device for agentId: ${agentId}`);

  return response.data;
};

export const removeDeviceFromHalcyon = async (agentId: string, token?: string) => {
  // remove the device
  try {
    await removeDevice(agentId, token);
  } catch (error) {
    Logger.error(`Error trying to remove device from Halcyon: ${error}`);
  }

  return true;
};
