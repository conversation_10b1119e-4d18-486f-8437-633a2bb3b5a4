import axios from 'axios';

import { HalcyonAuthResponse } from '../types/';

import { handleHalcyonErrors } from '../utils/halcyon-errors';

import { HALCYON_BASE_URL, HALCYON_USERNAME, HALCYON_PASSWORD } from '@shared/constants/env';

const customAxios = axios.create({
  baseURL: HALCYON_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const getAccessToken = async (
  username: string = HALCYON_USERNAME,
  password: string = HALCYON_PASSWORD
) => {
  const body = {
    username: username,
    password: password,
  };
  try {
    const response = await customAxios.post('/identity/auth/login', body);
    return response.data as HalcyonAuthResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};
