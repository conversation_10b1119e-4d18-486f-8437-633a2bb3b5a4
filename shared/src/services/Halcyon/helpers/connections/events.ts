import axios from 'axios';

import {
  EventResponse,
  EventsResponse,
  EventMetricsResponse,
  MonthMetricResponse,
  PartitionedMetricsResponse,
} from '../types/';
import { getAccessKeys, refreshAccessKeys } from '../functions/authentication.functions';

import { errors } from '@shared/utils/app-errors';
import { handleHalcyonErrors } from '../utils/halcyon-errors';

import { HALCYON_BASE_URL, HALCYON_TENANT_ID } from '@shared/constants/env';

const customAxios = axios.create({
  baseURL: HALCYON_BASE_URL + '/v1/event',
  headers: { 'X-TenantID': HALCYON_TENANT_ID },
});

// before request (makes the call on the first place)
customAxios.interceptors.request.use(
  async (config) => {
    // getting auth info
    let accessToken = config.params.token;

    // its a halcyon crontroller's request
    if (!accessToken) {
      try {
        accessToken = await getAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) {
        return Promise.reject(errors.halcyon_authentication_failed());
      }
    }

    if (config.headers) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

// if theres and error after the request
customAxios.interceptors.response.use(
  (response) => {
    // if it was successful
    return response;
  },
  async function (error) {
    // this block update the bearer token using the refresh if exists
    const originalRequest = error.config;
    if (error.response.status === 401 && !originalRequest._retry) {
      // getting auth info
      let accessToken = originalRequest.params.token;
      // If the token cames from outside it can't be updated from here
      if (accessToken) {
        throw error;
      }
      // Retry if the token cames from halcyon database
      originalRequest._retry = true;

      // getting refreshToken from db
      try {
        accessToken = await refreshAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) return Promise.reject(errors.halcyon_authentication_failed());
      originalRequest.headers = {
        ...(originalRequest.headers || {}),
        Authorization: `Bearer ${accessToken}`,
      };
      return customAxios(originalRequest);
    }
    // if the error its anything else but auth error it throws the original error
    return Promise.reject(error);
  }
);

export const getHalcyonEvents = async (
  token?: string,
  page?: string | number,
  page_size?: string | number,
  start_time?: string,
  end_time?: string
) => {
  try {
    const result = await customAxios.get('/', {
      params: { token, page_size, page, start_time, end_time },
    });
    return result.data as EventsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getHalcyonEvent = async (eventID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${eventID}`, {
      params: { token },
    });
    return result.data as EventResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getHalcyonEventMetrics = async (
  token?: string,
  start_time?: string,
  end_time?: string
) => {
  try {
    const result = await customAxios.get('/metrics', {
      params: { token, start_time, end_time },
    });
    return result.data as EventMetricsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getHalcyonEventMetricsByMonth = async (token?: string) => {
  try {
    const result = await customAxios.get('/metrics-by-month', {
      params: { token },
    });
    return result.data as MonthMetricResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getCriticalHalcyonEvents = async (
  token?: string,
  page?: string | number,
  page_size?: string | number,
  start_time?: string,
  end_time?: string
) => {
  try {
    const result = await customAxios.get('/critical', {
      params: { token, page, page_size, start_time, end_time },
    });
    return result.data as EventsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getPartitionedHalcyonEvents = async (
  start_time: string,
  end_time: string,
  token?: string
) => {
  try {
    const result = await customAxios.get('/partitioned-metrics', {
      params: { start_time, end_time, token },
    });
    return result.data as PartitionedMetricsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getRelatedHalcyonEvents = async (
  eventID: string,
  token?: string,
  page?: string | number,
  page_size?: string | number
) => {
  try {
    const result = await customAxios.get(`${eventID}/related`, {
      params: { token, page, page_size },
    });
    return result.data as EventsResponse; // this hasn't been proved
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};
