import axios from 'axios';

import { EventsResponse, ThreatHalcyonResponse, BasicHalcyonBody } from '../types';
import { getAccessKeys, refreshAccessKeys } from '../functions/authentication.functions';

import { errors } from '@shared/utils/app-errors';
import { handleHalcyonErrors } from '../utils/halcyon-errors';

import { HALCYON_BASE_URL, HALCYON_TENANT_ID } from '@shared/constants/env';

const customAxios = axios.create({
  baseURL: HALCYON_BASE_URL + '/v1/threat',
  headers: { 'X-TenantID': HALCYON_TENANT_ID },
});

// before request (makes the call on the first place)
customAxios.interceptors.request.use(
  async (config) => {
    // getting auth info
    let accessToken = config.params.token;

    // its a halcyon crontroller's request
    if (!accessToken) {
      try {
        accessToken = await getAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) {
        return Promise.reject(errors.halcyon_authentication_failed());
      }
    }
    if (config.headers) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

// if theres and error after the request
customAxios.interceptors.response.use(
  (response) => {
    // if it was successful
    return response;
  },
  async function (error) {
    // this block update the bearer token using the refresh if exists
    const originalRequest = error.config;
    if (error.response.status === 401 && !originalRequest._retry) {
      // getting auth info
      let accessToken = originalRequest.params.token;
      // If the token cames from outside it can't be updated from here
      if (accessToken) {
        throw error;
      }
      // Retry if the token cames from halcyon database
      originalRequest._retry = true;

      // getting refreshToken from db
      try {
        accessToken = await refreshAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) return Promise.reject(errors.halcyon_authentication_failed());
      originalRequest.headers = {
        ...(originalRequest.headers || {}),
        Authorization: `Bearer ${accessToken}`,
      };
      return customAxios(originalRequest);
    }
    // if the error its anything else but auth error it throws the original error
    return Promise.reject(error);
  }
);

export const getHalcyonThreat = async (threatID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${threatID}`, {
      params: { token }, // token passed by params if is a custom token
    });
    return result.data as ThreatHalcyonResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getHalcyonEventsFromThreat = async (
  threatID: string,
  token?: string,
  page?: number | string,
  page_size?: number | string
) => {
  try {
    const result = await customAxios.get(`/${threatID}/events`, {
      params: { token, page, page_size }, // token passed by params if is a custom token
    });
    return result.data as EventsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const unblockHalcyonFile = async (threatID: string, token?: string) => {
  try {
    const result = await customAxios.post(`/${threatID}/allow`, undefined, {
      params: { token },
    });
    return result.data as BasicHalcyonBody; // need this type
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const downloadHalcyonFile = async (threatID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${threatID}/download`, {
      params: { token },
    });
    return result.data as BasicHalcyonBody; // need this type
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};
