import axios from 'axios';

import { DeviceResponse, DevicesResponse, EventsResponse, HalcyonDeviceMetrics } from '../types/';
import { getAccessKeys, refreshAccessKeys } from '../functions/authentication.functions';

import { errors } from '@shared/utils/app-errors';
import { handleHalcyonErrors } from '../utils/halcyon-errors';

import { HALCYON_BASE_URL, HALCYON_TENANT_ID } from '@shared/constants/env';

const customAxios = axios.create({
  baseURL: HALCYON_BASE_URL + '/v1/device',
  headers: { 'X-TenantID': HALCYON_TENANT_ID },
});

// before request (makes the call on the first place)
customAxios.interceptors.request.use(
  async (config) => {
    // getting auth info
    let accessToken = config.params.token;

    // its a halcyon crontroller's request
    if (!accessToken) {
      try {
        accessToken = await getAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) {
        return Promise.reject(errors.halcyon_authentication_failed());
      }
    }

    if (config.headers) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }

    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

// if theres and error after the request
customAxios.interceptors.response.use(
  (response) => {
    // if it was successful
    return response;
  },
  async function (error) {
    // this block update the bearer token using the refresh if exists
    const originalRequest = error.config;
    if (error.response.status === 401 && !originalRequest._retry) {
      // getting auth info
      let accessToken = originalRequest.params.token;
      // If the token cames from outside it can't be updated from here
      if (accessToken) {
        throw error;
      }
      // Retry if the token cames from halcyon database
      originalRequest._retry = true;

      // getting refreshToken from db
      try {
        accessToken = await refreshAccessKeys();
      } catch (err) {
        throw err;
      }
      if (!accessToken) return Promise.reject(errors.halcyon_authentication_failed());
      originalRequest.headers = {
        ...(originalRequest.headers || {}),
        Authorization: `Bearer ${accessToken}`,
      };
      return customAxios(originalRequest);
    }
    // if the error its anything else but auth error it throws the original error
    return Promise.reject(error);
  }
);

export const getDevices = async (
  token?: string,
  page?: number | string,
  page_size?: number | string
) => {
  try {
    const result = await customAxios.get('/', {
      params: { token, page, page_size },
    });
    return result.data as DevicesResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const getDevice = async (deviceID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${deviceID}`, {
      params: { token }, // token passed by params if is a custom token
    });
    return result.data as DeviceResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const removeDevice = async (deviceID: string, token?: string) => {
  try {
    await customAxios.delete(`/${deviceID}`, {
      params: { token },
    });
    return true;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const deviceEvents = async (
  deviceID: string,
  token?: string,
  page?: string | number,
  page_size?: string | number
) => {
  try {
    const result = await customAxios.get(`/${deviceID}/events`, {
      params: { token, page_size, page },
    });
    return result.data as EventsResponse;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const needsAttention = async (
  token?: string,
  page?: string | number,
  page_size?: string | number
) => {
  try {
    const result = await customAxios.get('/need-attention/', {
      params: { token, page_size, page },
    });
    return result.data as DevicesResponse;
    return true;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const deviceMetrics = async (token?: string) => {
  try {
    const result = await customAxios.get('/metrics/', {
      params: { token },
    });
    return result.data as HalcyonDeviceMetrics;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

/**
 * WE NEED SOME SAMPLES FOR THESE ROUTES
 */
export const deviceExtractedKeys = async (deviceID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${deviceID}/extracted_keys`, {
      params: { token },
    });
    return true;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};

export const deviceExtractedKey = async (deviceID: string, keyID: string, token?: string) => {
  try {
    const result = await customAxios.get(`/${deviceID}/extracted_keys/${keyID}`, {
      params: { token },
    });
    return true;
  } catch (err: any) {
    throw handleHalcyonErrors(err);
  }
};
