import { AxiosError } from 'axios';

import { generateError } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

export const handleEntraIDErrors = (error: AxiosError) => {
  if (!error.isAxiosError) {
    Logger.error(`EntraID API error: ${error}`);
    throw generateError('Error communicating with EntraID API', 500, 'ENTRAID_ERROR');
  }

  const response = error.response as {
    data: {
      error: {
        code: string;
        message: string;
      };
    };
    status?: number;
  };

  if (!response) {
    Logger.error(`EntraID API error: ${error}`);
    throw generateError('Error communicating with EntraID API', 500, 'ENTRAID_ERROR');
  }

  throw generateError(
    response.data.error.message,
    response.data.error.code === 'Request_ResourceNotFound' ? 404 : 500,
    response.data.error.code.toUpperCase()
  );
};
