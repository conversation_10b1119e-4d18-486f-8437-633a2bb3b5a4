import { ConfidentialClientApplication, Configuration, LogLevel } from '@azure/msal-node';

import {
  ENTRAID_CLIENT_ID,
  ENTRAID_CLIENT_SECRET,
  ENTRAID_DIRECTORY_ID,
} from '@shared/constants/env';

import { handleEntraIDErrors } from './entraid-errors';
import { errors } from '@shared/utils/app-errors';

let cachedAccessToken: string | null = null;
let tokenExpiry: number | null = null;

let msalConfig: Configuration | null = null;

if (ENTRAID_CLIENT_ID && ENTRAID_CLIENT_SECRET && ENTRAID_DIRECTORY_ID) {
  msalConfig = {
    auth: {
      clientId: ENTRAID_CLIENT_ID,
      authority: `https://login.microsoftonline.com/${ENTRAID_DIRECTORY_ID}`,
      clientSecret: ENTRAID_CLIENT_SECRET,
    },
    system: {
      loggerOptions: {
        loggerCallback(loglevel, message, containsPii) {
          console.log(message);
        },
        logLevel: LogLevel.Error,
      },
    },
  };
}

export const getAccessToken = async (): Promise<string> => {
  if (cachedAccessToken && tokenExpiry && tokenExpiry > Date.now()) {
    return cachedAccessToken;
  }

  if (!msalConfig) {
    throw errors.entraid_authentication_error();
  }

  const cca = new ConfidentialClientApplication(msalConfig);

  try {
    const result = await cca.acquireTokenByClientCredential({
      scopes: ['https://graph.microsoft.com/.default'],
    });

    if (result && result.accessToken && result.expiresOn) {
      cachedAccessToken = result.accessToken;
      tokenExpiry = result.expiresOn.getTime() * 1000 - 60000; // Set expiry 1 minute earlier for buffer
      return cachedAccessToken;
    } else {
      throw errors.entraid_authentication_error();
    }
  } catch (error: any) {
    throw handleEntraIDErrors(error);
  }
};
