import axios from 'axios';

import { ENTRAID_BASE_URL } from '@shared/constants/env';

import { getAccessToken, handleEntraIDErrors } from '../utils';

import { EntraIDUser } from '../models';

const customAxios = axios.create({
  baseURL: `${ENTRAID_BASE_URL}`,
  headers: {
    'Content-Type': 'application/json',
  },
});

customAxios.interceptors.request.use(
  async (config) => {
    const token = await getAccessToken();
    config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const getEntraUserId = async (username: string): Promise<string> => {
  try {
    const response = await customAxios.get(`/v1.0/users/${username}`);

    const userData = response.data as EntraIDUser;

    return userData.id;
  } catch (error: any) {
    return handleEntraIDErrors(error);
  }
};

export const revokeSignInEntraIDSessions = async (userId: string): Promise<void> => {
  try {
    await customAxios.post(`/v1.0/users/${userId}/revokeSignInSessions`);
  } catch (error: any) {
    return handleEntraIDErrors(error);
  }
};
