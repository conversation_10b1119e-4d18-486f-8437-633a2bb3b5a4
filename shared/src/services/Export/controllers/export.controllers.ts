import { Request, Response } from 'express';
import { json2csv } from 'json-2-csv';

import { Column } from '@shared/services/Export/helpers/interfaces';
import { GetAllQuery } from '@shared/types';
import getJsonToExport from '@shared/services/Export/helpers/functions/export.function';
import { errors } from '@shared/utils/app-errors';
import { isArrayLike } from 'lodash';

export const generateExportFileOf = async <T extends Record<string, any> | Record<string, any>[]>(
  req: Request | Record<string, any>,
  filter: string | undefined,
  sort: string | undefined,
  columns: Column[],
  format: string,
  getData: (
    filter?: string,
    limit?: number,
    offset?: number,
    sort?: string,
    req?: Request | Record<string, any>
  ) => Promise<T>,
  transform?: (params: T) => Record<string, any>[]
) => {
  const responseData = await getData(filter, -1, 0, sort, req);
  const transformedData = transform ? transform(responseData) : responseData;
  if (isArrayLike(transformedData) && transformedData.length < 1)
    throw errors.not_found('Resources');

  const jsonToExport = await getJsonToExport(transformedData as Record<string, any>[], columns);

  let dataToExport: any;
  switch (format.toLowerCase()) {
    case 'json':
      dataToExport = JSON.stringify(jsonToExport);
      break;
    case 'csv':
      const sortedColumns = columns.sort((a, b) => a.order - b.order);
      const sortedTitles = sortedColumns.map((column) => column.title);
      dataToExport = json2csv(jsonToExport, {
        checkSchemaDifferences: true,
        delimiter: {
          field: ',',
          wrap: '"',
          eol: '\n',
        },
        emptyFieldValue: '',
        keys: sortedTitles,
        trimFieldValues: true,
        trimHeaderFields: true,
        preventCsvInjection: true,
      });
      break;
    default:
      dataToExport = '';
      break;
  }

  return Buffer.from(dataToExport);
};

export const exportFileOf = async <T extends Record<string, any> | Record<string, any>[]>(
  req: Request,
  res: Response,
  getData: (
    filter?: string,
    limit?: number,
    offset?: number,
    sort?: string,
    req?: Request | Record<string, any>
  ) => Promise<T>,
  transform?: (params: T) => Record<string, any>[]
) => {
  const { filter, sort } = req.query as GetAllQuery;
  const { columns, format } = req.body as { columns: Column[]; format: string };

  const exportedFile = await generateExportFileOf(
    req,
    filter,
    sort,
    columns,
    format,
    getData,
    transform
  );

  return res.status(200).send(exportedFile);
};
