import { Gateway } from '@shared/helpers/classes/gateway.class';

import { getFieldsValues } from './fields.function';
import { FORMATTERS } from './format.function';
import { JSONData, Column } from '../interfaces/index';

export const getJsonToExport = async (
  data: Record<string, any>[],
  columns: Column[]
): Promise<JSONData[]> => {
  const hasDatetimeColumn = columns.some((column) => column.format === 'datetime');

  let timezone = undefined;
  if (hasDatetimeColumn) {
    timezone = Gateway.getTenantLoadedTimezone();
  }

  const formattedData: JSONData[] = [];

  for (const item of data) {
    const formattedItem: JSONData = {};

    for (const column of columns) {
      const {
        title,
        field,
        fields,
        format,
        formatParams,
        translations,
        default: defaultValue,
      } = column;
      const formatFunction = FORMATTERS[format];
      const fieldsToFind: string[] = field ? [field] : fields || [];

      let value = await formatFunction(
        getFieldsValues(fieldsToFind, item),
        hasDatetimeColumn && format === 'datetime' ? { ...formatParams, timezone } : formatParams
      );
      if (format === 'string' && translations && translations[value] !== undefined)
        value = translations[value];
      if (!value && defaultValue) value = defaultValue;

      formattedItem[title] = value;
    }

    formattedData.push(formattedItem);
  }

  return formattedData;
};

export default getJsonToExport;
