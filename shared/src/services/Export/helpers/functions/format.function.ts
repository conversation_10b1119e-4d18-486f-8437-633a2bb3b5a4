import { formatDate } from '../utils/date.utils';
import { Formatter, FormatParams } from '../interfaces';
import { getFieldValue } from './fields.function';

const formatBoolean = async (
  data: any[],
  formatParams?: FormatParams
): Promise<string | boolean> => {
  const bool = data.every((value: any) => {
    if (formatParams?.condition) {
      const {
        condition: { type, value: expectedValue },
      } = formatParams;
      if (type === 'equal') return value === expectedValue;
    }
    return Boolean(value);
  });

  if (formatParams?.true && formatParams?.false) {
    const { true: trueValue, false: falseValue } = formatParams;
    return bool ? trueValue : falseValue;
  }
  return Boolean(bool);
};

const formatString = async (data: any[]): Promise<string> => String(data.join(', '));

const formatIp = async (data: any[]): Promise<string> => {
  const [address] = data;
  let validIps = [address];

  if (data[1] !== null) {
    const [__, [ipv4, ..._]] = data;
    validIps = [address, ipv4].filter((value) => value && value.length > 0);
  }
  return validIps.join(' / ');
};

const formatArray = async (data: any[], formatParams: FormatParams | undefined): Promise<string> =>
  data[0]
    .map((item: any) => {
      if (formatParams?.key) {
        const { key } = formatParams;
        return getFieldValue(key, item);
      }
      return String(item);
    })
    .filter((value: string) => value && value.length > 0)
    .join(', ');

const formatDatetime = async (
  data: any[],
  formatParams: FormatParams | undefined
): Promise<string | null> => {
  const locale = formatParams?.locale ? formatParams.locale : 'en';
  return formatDate(data[0], locale, formatParams?.timezone);
};

const formatNumber = async (data: any[]): Promise<number> =>
  data
    .map((value: string | number) => Number(value))
    .reduce((sum: number, current: number) => sum + current, 0);

const formatLength = async (data: any[]): Promise<number> =>
  data
    .map((value: any[]) => Number(value.length))
    .reduce((sum: number, current: number) => sum + current, 0);

export const FORMATTERS: { [key: string]: Formatter } = {
  array: formatArray,
  boolean: formatBoolean,
  datetime: formatDatetime,
  ip: formatIp,
  length: formatLength,
  number: formatNumber,
  string: formatString,
};
