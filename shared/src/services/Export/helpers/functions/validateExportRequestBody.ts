import { RequestHandler } from 'express';
import { body } from 'express-validator';

export const validateExportRequestBody: RequestHandler = Object.freeze([
  body('columns').isArray({ min: 1 }),
  body('columns.*')
    .isObject()
    .custom((v) => {
      if (v.field === undefined && v.fields === undefined) {
        throw Error("Must either define 'field' or 'fields'");
      }
      return true;
    }),
  body('columns.*.id').optional().isString(),
  body('columns.*.order').optional().isInt({ min: 1 }),
  body('columns.*.title').isString(),
  body('columns.*.format').custom((v) => {
    if (!['string', 'boolean', 'ip', 'array', 'datetime', 'number'].includes(v)) {
      throw Error(
        "Must be one of the following: 'string', 'boolean', 'ip', 'array', 'datetime', 'number'"
      );
    }
    return true;
  }),
  body('columns.*.formatParams').optional().isObject(),
  body('columns.*.formatParams.key').optional().isString(),
  body('columns.*.formatParams.true').optional().isString(),
  body('columns.*.formatParams.false').optional().isString(),
  body('columns.*.formatParams.locale').optional(),
  body('columns.*.formatParams.timezone').optional().isString(),
  body('columns.*.formatParams.condition.type')
    .optional()
    .custom((v) => v == 'equal'),
  body('columns.*.formatParams.condition.value').optional().isString(),
  body('columns.*.field').optional().isString(),
  body('columns.*.fields').optional().isArray(),
  body('columns.*.fields.*').optional().isString(),
  body('columns.*.default').optional().isString(),
  body('columns.*.translations').optional().isObject(),
  body('format').custom((v) => {
    if (v != 'csv' && v != 'json') {
      throw Error('Must be either csv or json');
    }
    return true;
  }),
]) as unknown as RequestHandler; // force the cast to suppress false flags
