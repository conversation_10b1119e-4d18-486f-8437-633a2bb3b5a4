/**
 * A utility function to fetch all available from paginated requests that return a total amount of items for the given request
 * @param fn A function who's only argument is the offset and returns a page of data
 * @param getTotalItems A function that parses the page of data and gets the total amount of items
 * @param getDataFromRequest A function that parses a page of data and returns the array of items
 * @param maxLimit The maximum amount of items each page can retrieve
 * @returns A single page whose array of data includes the data of all other pages
 */

export async function getAndJoinAllPages<T, K>(
  fn: (offset: number) => Promise<T>,
  getTotalItems: (response: T) => number,
  getDataFromRequest: (response: T) => K[],
  maxLimit: number = 100
): Promise<K[]> {
  const initialResponse = await fn(0);

  const total_items = getTotalItems(initialResponse);

  const initialResponseDataArray = getDataFromRequest(initialResponse);

  if (total_items < maxLimit) return initialResponseDataArray;

  const promises = [];
  for (let i = maxLimit; i < total_items; i += maxLimit) {
    promises.push(fn(i).then((res) => initialResponseDataArray.concat(getDataFromRequest(res))));
  }
  await Promise.all(promises);
  return initialResponseDataArray;
}
