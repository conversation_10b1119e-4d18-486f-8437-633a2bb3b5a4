import { JSONData } from '../interfaces/json.interface';

export const getFieldValue = (field: string, item: JSONData): any => {
  const fieldNames = field.split('.');
  let value = item;

  for (const fieldName of fieldNames) {
    if (!value || !(fieldName in value)) return null;
    value = value[fieldName];
  }

  return value;
};

export const getFieldsValues = (fields: string[], item: JSONData): any => {
  return fields.map((field) => getFieldValue(field, item));
};
