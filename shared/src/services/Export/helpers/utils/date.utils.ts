import moment from 'moment';

import 'moment/locale/en-gb'; // Import English locale
import 'moment/locale/es';

import { dateWithTimeZone } from '@shared/helpers/functions/date.function';

export function formatDate(
  dateValue: Date,
  locale: string = 'en',
  timeZone: string = '',
  dateFormat: string = 'YYYY-MM-DD-HH-mm-ss'
): string | null {
  if (!dateValue) return null;

  const _dateWithTimezone = dateWithTimeZone(dateValue, timeZone);

  // Moment.js uses different locale keys, adjust as needed
  const momentLocale = locale === 'en' ? 'en-gb' : locale;

  // Set the locale
  moment.locale(momentLocale);

  // Create a Moment.js object from the date
  const date = moment(_dateWithTimezone);

  // Format the date
  return date.format(dateFormat);
}
