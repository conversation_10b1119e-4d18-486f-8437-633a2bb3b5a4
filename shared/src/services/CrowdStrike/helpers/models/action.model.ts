import { Types } from 'mongoose';

export interface ActionModel {
  console: Types.ObjectId;
  label: string;
  command: string;
  retryTime: number; // Sirve para asignar el tiempo de duracion del comando, para obtener resultados de rtr
  platform: string;
  so: Array<string>;
  order: number;
}

export interface EncryptActionModel {
  assetId: string;
  assetName: string;
  detectionId: string;
  action: Types.ObjectId;
  expire: Date;
}

export interface ActionButtonModel {
  type: string;
  subType: string;
  data: Types.ObjectId;
}

export interface AppButtonModel {
  label: string;
  id: Types.ObjectId;
}
