import { SchemaBasicModel } from '@shared/models';

export interface ConsolePayload extends SchemaBasicModel {
  name: string;
  apiUrl: string;
  clientId: string;
  secretId: string;
  licenses: number;
  autoContainmentTime: number; // en segundos
  expiringButtonTime: number; // en segundos
  activeSeverities: string[]; // severidades consideradas dentro de horario laboral
  inactiveSeverities: string[]; // severidades consideradas fuera de horario laboral
  activeAutoContainment: boolean; // configuracion de auto contencion dentro de horario laboral
  activeManualContainment: boolean; // configuracion de contencion dentro de horario laboral
  inactiveAutoContainment: boolean; // configuracion de auto contencion fuera de horario laboral
  inactiveManualContainment: boolean; // configuracion de contencion fuera de horario laboral
  activeActions: boolean;
  inactiveActions: boolean;
}

export interface ConsoleModel extends ConsolePayload {
  token: string;
}
