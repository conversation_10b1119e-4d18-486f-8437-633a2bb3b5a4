import { Types } from 'mongoose';

export interface AutoContainmentModel {
  console: Types.ObjectId;
  assetId: string;
  assetName: string;
  trigger: boolean;
  time: Date; // el tiempo de contencion no debe ser mayor a 6 horas
}

// esto es porque el tiempo de expiracion de los botones de liberar, contener y liberar autocontencion tienen una validez maxima de 6 horas

export interface ContainmentActionModel {
  type: 'lift_autocont' | 'contain' | 'lift_containment';
  consoleId: Types.ObjectId;
  assetId: string;
  assetName: string;
  expire: Date;
}

export const containmentType = ['lift_autocont', 'contain', 'lift_containment'];
