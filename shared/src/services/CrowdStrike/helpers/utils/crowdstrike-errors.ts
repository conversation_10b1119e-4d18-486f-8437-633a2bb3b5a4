import { AxiosError } from 'axios';
import { generateError } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

// this function handle crowdstrike errors in responses
export const handlerCsErrors = (error: AxiosError) => {
  Logger.error('CrowdStrike API error:', error);

  if (!error.isAxiosError) {
    throw generateError('Error communicating with CrowdStrike API', 500, 'CROWDSTRIKE_ERROR');
  }

  const response = error.response as {
    data: { meta: object[]; errors: { code: number; message: string }[] };
  };

  if (!response?.data) {
    throw generateError('Error communicating with CrowdStrike API', 500, 'CROWDSTRIKE_ERROR');
  }

  const data = response.data;
  if (!data.errors) {
    throw generateError('Error communicating with CrowdStrike API', 500, 'CROWDSTRIKE_ERROR');
  }

  const csErrors = data.errors;
  if (csErrors.length > 1) {
    const messages: string[] = csErrors.map(
      (error) => `Error: ${error.message}, Code: ${error.code}`
    );
    throw generateError(messages.join('\n'), 500, 'CROWDSTRIKE_MULTIPLE_ERRORS');
  } else if (csErrors.length === 1) {
    throw generateError(csErrors[0].message, csErrors[0].code, 'CROWDSTRIKE_ERROR');
  } else {
    throw generateError('Error communicating with CrowdStrike API', 500, 'CROWDSTRIKE_ERROR');
  }
};
