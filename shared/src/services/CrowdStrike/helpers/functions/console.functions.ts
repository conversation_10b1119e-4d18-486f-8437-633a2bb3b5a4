import { Types } from 'mongoose';

import { Console } from '../../schemas';

import { errors } from '@shared/utils/app-errors';

const crowdStrikeSeverities = ['Critical', 'High', 'Medium', 'Low', 'Informational'];

export const getAuthInfo = async (consoleId: Types.ObjectId) => {
  const _console = await Console.findById(consoleId);

  if (!_console) {
    throw errors.not_found('Console');
  }

  return { token: _console.token, url: _console.apiUrl };
};

export const validateSeverities = (severities: string[]) => {
  for (const sevetiry of severities) {
    if (!crowdStrikeSeverities.includes(sevetiry)) {
      return false;
    }
  }
  return true;
};
