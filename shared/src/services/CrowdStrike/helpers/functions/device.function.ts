// Dependencies
import { Types } from 'mongoose';

// Connections & functions
import { getAuthInfo } from '.';
import { getDevicesIds, getDevicesDetails } from '../connection';

// Helpers
import { errors } from '@shared/utils/app-errors';

export const searchDevices = async (value: string, consoleId: Types.ObjectId) => {
  const { token, url } = await getAuthInfo(consoleId);
  // searching for devices ids based on user input
  let devicesIds: {
    resources: string[];
    errors: { code: number; message: string }[];
  };
  try {
    devicesIds = await getDevicesIds(value, url, token);
  } catch (err) {
    throw err;
  }

  if (devicesIds.resources.length === 0) {
    throw errors.empty_devices_ids();
  }

  // Getting details from ids
  let devicesDetails: {
    resources: {
      device_id: string;
      hostname: string;
      platform_name: string;
      os_version: string;
    }[];
    errors: { code: number; message: string }[];
  };
  try {
    devicesDetails = await getDevicesDetails(devicesIds.resources, url, token);
  } catch (err) {
    throw err;
  }

  // Getting devices names
  const devices = devicesDetails.resources.map((device) => {
    return {
      id: device.device_id,
      name: device.hostname,
      platform: device.platform_name,
      os: device.os_version,
    };
  });

  if (devices.length === 0) {
    throw errors.empty_devices_ids();
  }

  return devices;
};
