import { Types } from 'mongoose';

import { Action, Encrypt } from '../../../schemas';

import { isExpired, cleanExpiredEncrypts, executeCommand, getRtrResult, openRtrSession } from '..';

import { getAuthInfo } from '../console.functions';
import { delay } from '@shared/utils/delay';
import { errors } from '@shared/utils/app-errors';

export const validateEncrypt = async (callback: string | Types.ObjectId) => {
  const existingEncrypt = await Encrypt.findById(callback);
  if (!existingEncrypt) throw errors.action_expired();

  const requestedAction = await Action.findById(existingEncrypt.action);
  if (!requestedAction) throw errors.not_found('CrowdStrike action');

  if (isExpired(+existingEncrypt.expire)) {
    await cleanExpiredEncrypts();
    return {
      valid: false,
      actionId: existingEncrypt.action,
      assetId: existingEncrypt.assetId,
      assetName: existingEncrypt.assetName,
      actionName: requestedAction.label,
    };
  }
  return {
    valid: true,
    actionId: existingEncrypt.action,
    assetId: existingEncrypt.assetId,
    assetName: existingEncrypt.assetName,
    actionName: requestedAction.label,
  };
};

export const executeRtr = async (actionId: Types.ObjectId, assetId: string) => {
  const requestedAction = await Action.findById(actionId);
  if (!requestedAction) throw errors.not_found('CrowdStrike action');

  const { token, url } = await getAuthInfo(requestedAction.console);
  const rtrSessionId = await openRtrSession(assetId, url, token).catch((err) => {
    throw err;
  });

  const cloudRequestId = await executeCommand(requestedAction.command, rtrSessionId, url, token);

  return cloudRequestId;
};

export const searchRtrResult = async (
  cloudRequest: {
    assetId: string;
    cloudRequestId: string;
  },
  actionId: Types.ObjectId
) => {
  const requestedAction = await Action.findById(actionId);
  if (!requestedAction) throw errors.not_found('CrowdStrike action');

  const { token, url } = await getAuthInfo(requestedAction.console);
  const result = await getRtrResult(cloudRequest, url, token).catch((err) => {
    throw err;
  });
  if (!result.complete) {
    return false;
  }

  return result;
};

export const retrySearchResult = async (
  cloudRequest: { assetId: string; cloudRequestId: string },
  actionId: Types.ObjectId
) => {
  const requestedAction = await Action.findById(actionId);
  if (!requestedAction) throw errors.not_found('CrowdStrike action');

  const { token, url } = await getAuthInfo(requestedAction.console);
  let result = await getRtrResult(cloudRequest, url, token).catch((err) => {
    throw err;
  });

  let searchResponse: boolean = result.complete;

  while (!searchResponse) {
    result = await getRtrResult(cloudRequest, url, token).catch((err) => {
      throw err;
    });

    searchResponse = result.complete;

    await delay(1000);
  }
  return result;
};
