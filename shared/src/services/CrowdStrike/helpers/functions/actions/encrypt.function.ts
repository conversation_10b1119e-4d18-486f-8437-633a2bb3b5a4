// Dependencies
import { ActionDocument, Encrypt } from '../../../schemas';

// Models
import { ActionButtonModel, AppButtonModel, DetectionModel } from '../../models';
import { TelegramButtonModel } from '@shared/services/Bot/helpers/models/misc/buttons.model';

export const createEncryptList = async (
  actions: Array<ActionDocument>,
  assetId: string,
  assetName: string,
  detectionId: string,
  expireButtonTime: number
): Promise<ActionButtonModel[]> => {
  const actionButtons: Array<ActionButtonModel> = [];

  const expireDate = new Date(Date.now() + expireButtonTime * 1000);

  for (let i = 0; i < actions.length; i++) {
    const action = actions[i];

    if (action) {
      const newEncryptAction = await Encrypt.create({
        assetId: assetId,
        assetName: assetName,
        detectionId: detectionId,
        action: action.id,
        expire: expireDate,
      });

      actionButtons.push({
        type: 'execute:crowdstrike.rtr',
        subType: action.label,
        data: newEncryptAction.id,
      });
    }
  }

  return actionButtons;
};

export const sortTelegramButtons = (buttons: ActionButtonModel[]) => {
  const sortedTelegramMarkup: TelegramButtonModel[][] = [];

  buttons.reduce((result, _, index, array) => {
    if (index % 2 === 0) {
      const row = array.slice(index, index + 2);
      const telegramButtonRow: TelegramButtonModel[] = [];

      for (let i = 0; i < row.length; i++) {
        const button = row[i];

        const formattedButton: TelegramButtonModel = {
          subType: button.subType,
          encrypt: { type: button.type, data: button.data },
        };
        telegramButtonRow.push(formattedButton);
      }

      sortedTelegramMarkup.push(telegramButtonRow);
    }
    return result;
  }, []);

  return sortedTelegramMarkup;
};

export const formatAppButtons = (buttons: ActionButtonModel[]) => {
  const formattedAppButtons: AppButtonModel[] = [];

  for (let i = 0; i < buttons.length; i++) {
    const button = buttons[i];

    const formattedButton: AppButtonModel = {
      label: button.subType,
      id: button.data,
    };
    formattedAppButtons.push(formattedButton);
  }

  return formattedAppButtons;
};

export const isExpired = (timestamp: number) => {
  if (Date.now() < timestamp) {
    return false;
  } else {
    return true;
  }
};

export const getAssetInfo = (detection: DetectionModel) => {
  const assetName = detection.data.hostname;
  const osVersion = detection.data.os_version;
  const assetId = detection.data.sensor_id;
  const detectionId = detection.data.detection_id;
  const sha256 = detection.data.sha256_hash;
  return [assetId, assetName, osVersion, detectionId, sha256];
};

export const cleanExpiredEncrypts = async (): Promise<string> => {
  await Encrypt.deleteMany({
    expire: { $lt: new Date() },
  });

  return `Lista de acciones expiradas vaciada`;
};
