// Dependencies
import { Types } from 'mongoose';

// Schemas
import { Temp } from '@shared/schemas';

// Conections

// Functions
import { generateUniqueSessionId } from '@shared/helpers/functions/temp.functions';

// - Cuando se presiona el boton de rtr custom tiene que existir un registro ya en Temp
//   - teniendo la info que el resto de los botones pero identificando que es de tipo custom-rtr
//   - guarda todo lo mismo pero un expiring time
//   - enviando en respuesta el id del temp
// - el temp es el que genera el resto de entradas,
//   - Telegram, se genera un botton a partir de ese temp id

// - El manejo del input:
//   - se obtiene el comando, el sessionId
//   - se obtiene la data
//   - se expira el temp y elimina de la coleccion
//   - se consume la funcion para enviar comando
/**
 * Function for creating custom rtr temp register
 * @param command String
 * @param asset string[]
 * @param consoleId Types.ObjectId
 * @param expiringTime Number
 */
export const getCustomRtr = async (
  assets: string[],
  consoleId: Types.ObjectId,
  expiringTime?: number
) => {
  // Creating session id
  const newSessionId = await generateUniqueSessionId();

  const newTemp = await Temp.create({
    data: { consoleId, assets, command: '' },
    session: newSessionId,
    expire: expiringTime ? new Date(+new Date() + +expiringTime * 1000) : undefined,
  });

  return newTemp._id;
};
