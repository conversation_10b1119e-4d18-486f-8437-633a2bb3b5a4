import { startSession, Types } from 'mongoose';

import { AutoContainment, ContainmentButton } from '../../schemas';

import { errors } from '@shared/utils/app-errors';
import { containmentActions } from '../constants';
import { setContainStatus } from '../connection';
import { containmentType } from '@shared/services/CrowdStrike/helpers/models/auto-containment.model';
import { TelegramButtonModel } from '@shared/services/Bot/helpers/models';
import { ActionButtonModel } from '@shared/services/CrowdStrike/helpers/models';
import { sendTelegramMessage } from '@shared/helpers/functions/telegram.function';
import { getAuthInfo } from '.';

import { Logger } from '@shared/helpers/classes/logger.class';

// esta funcion se le pasa al cron para contener los equipos en lista y eliminar a los equipos que esten en la lista pero no se vayan a contener
export const autoContainmentConsoles = async (): Promise<string> => {
  const actualTime = new Date();
  const autoContentAssets = await AutoContainment.find({});

  for (const asset of autoContentAssets) {
    if (asset.trigger) {
      if (+asset.time <= +actualTime) {
        // Intentar contener equipo
        try {
          await useContainment(asset.console, asset.assetId, 'contain');
          Logger.warning(
            `[Auto contención] Se realizó una auto contención para el equipo: ${asset.assetName}`
          );
          // send notification to every medio
          sendNotification(asset.assetName);
        } catch (error) {
          Logger.error(
            `Error al intentar contener el equipo ${asset.assetName}. Autocontención ${asset.id}. Error: ${error}`
          );
        }
        await AutoContainment.deleteOne({ _id: asset._id });
      }
    } else {
      await AutoContainment.deleteOne({ _id: asset._id });
    }
  }

  return `Lista de equipos para autocontener verificada`;
};

//esta funcion agrega los equipos a la lista de autocontencion y crea botones para contener y liberar
export const pushAssetToContainmentQueue = async (
  consoleId: Types.ObjectId,
  assetId: string,
  assetName: string,
  autoContainmentTime: number,
  expiringButtonTime: number,
  isAutoContainmentEnabled: boolean,
  isManualContainmentEnabled: boolean
): Promise<
  | {
      type: string;
      subType: string;
      data: Types.ObjectId;
    }[]
  | undefined
> => {
  if (isAutoContainmentEnabled || isManualContainmentEnabled) {
    const actualTime = +new Date();

    if (isAutoContainmentEnabled) {
      const autoContentAsset = await AutoContainment.findOne({
        console: consoleId,
        assetId: assetId,
      });

      if (!autoContentAsset) {
        const newTriggerTime = new Date(actualTime + autoContainmentTime * 1000);

        await AutoContainment.create({
          console: consoleId,
          assetId: assetId,
          assetName: assetName,
          trigger: true,
          time: newTriggerTime,
        });
      }
    }

    // crear y retornar los botones
    const buttons = await getAutocontainmentButtons(
      consoleId,
      isAutoContainmentEnabled,
      isManualContainmentEnabled,
      expiringButtonTime,
      assetId,
      assetName,
      actualTime
    );
    return buttons;
  } else {
    return undefined;
  }
};

const getAutocontainmentButtons = async (
  consoleId: Types.ObjectId,
  isAutoContained: boolean,
  isManualContained: boolean,
  expiringTime: number,
  assetId: string,
  assetName: string,
  actualTime: number
) => {
  const expireTime = actualTime + expiringTime * 1000;
  const newContainmentButtons: Array<Types.ObjectId> = [];

  const session = await startSession();
  session.startTransaction();
  // crear los 3 botones
  for (const action of containmentActions) {
    if (isAutoContained || (isManualContained && action !== 'lift_autocont')) {
      const newContainmentAction = await ContainmentButton.create({
        type: action,
        consoleId: consoleId,
        assetId: assetId,
        assetName: assetName,
        expire: expireTime,
      });

      newContainmentButtons.push(newContainmentAction.id);
    }
  }
  await session.commitTransaction();

  let buttons: { type: string; subType: string; data: Types.ObjectId }[];

  if (newContainmentButtons.length === 2) {
    buttons = [
      {
        type: 'execute:crowdstrike.contain',
        subType: 'Contener',
        data: newContainmentButtons[0],
      },
      {
        type: 'execute:crowdstrike.lift_containment',
        subType: 'Liberar',
        data: newContainmentButtons[1],
      },
    ];
  } else {
    buttons = [
      {
        type: 'execute:crowdstrike.lift_autocont',
        subType: 'Liberar autocontención',
        data: newContainmentButtons[0],
      },
      {
        type: 'execute:crowdstrike.contain',
        subType: 'Contener',
        data: newContainmentButtons[1],
      },
      {
        type: 'execute:crowdstrike.lift_containment',
        subType: 'Liberar',
        data: newContainmentButtons[2],
      },
    ];
  }
  return buttons;
};

// esta funcion ejecuta la accion de contencion sobre el equipo y retorna la respuesta de crowdstrike
export const useContainment = async (
  consoleId: Types.ObjectId,
  assetId: string,
  actionType: 'lift_autocont' | 'contain' | 'lift_containment'
) => {
  if (actionType !== 'lift_autocont') {
    const { token, url } = await getAuthInfo(consoleId);
    // Ejecutar la acción
    const response = await setContainStatus(assetId, actionType, url, token);
    if (response.errors.length !== 0) {
      if (response.errors[0].code === 409) {
        throw errors.device_not_eligible_for(actionType);
      } else {
        throw new Error(response.errors[0].message);
      }
    }
  }

  // En todos los casos, eliminar todos los autocontainments para el equipo
  const result = await AutoContainment.deleteMany({
    console: consoleId,
    assetId: assetId,
  });

  // Indicar si se eliminaron autocontenciones
  return result.deletedCount > 0;
};

// funcion para obtener data del registro
export const validateContainmentButton = async (callback: string | Types.ObjectId) => {
  const executedButton = await ContainmentButton.findById(callback);

  if (!executedButton) {
    throw errors.not_found('ContainmentButton');
  }
  if (!containmentType.includes(executedButton.type)) {
    throw errors.containment_action_not_valid();
  }
  if (isExpired(+executedButton.expire)) {
    await clearExpiredContainedButtons();
    return {
      valid: false,
      type: executedButton.type,
      consoleId: executedButton.consoleId,
      assetId: executedButton.assetId,
      assetName: executedButton.assetName,
    };
  }
  return {
    valid: true,
    type: executedButton.type,
    consoleId: executedButton.consoleId,
    assetId: executedButton.assetId,
    assetName: executedButton.assetName,
  };
};

const isExpired = (timestamp: number) => {
  if (Date.now() < timestamp) {
    return false;
  } else {
    return true;
  }
};

//esta funcion va a limpiar los botones de contencion que ya hayan expirado
export const clearExpiredContainedButtons = async () => {
  await ContainmentButton.deleteMany({
    expire: { $lt: new Date() },
  });

  return `Lista de acciones de contención expiradas vaciada`;
};

/**
 * TELEGRAM FUNCTION
 */

export const sortContainmentTelegramButtons = (buttons: ActionButtonModel[]) => {
  const parsedContainmentButtons: TelegramButtonModel[] = [];
  for (const button of buttons) {
    const formattedButton: TelegramButtonModel = {
      subType: button.subType,
      encrypt: { type: button.type, data: button.data },
    };
    parsedContainmentButtons.push(formattedButton);
  }
  let sortedTelegramMarkup: TelegramButtonModel[][] = [];

  // TODO: Improve this logic. I'm patching it for now with this condition to prevent errors.
  if (parsedContainmentButtons.length > 1) {
    if (parsedContainmentButtons.length === 3) {
      sortedTelegramMarkup = [
        [
          {
            subType: parsedContainmentButtons[0].subType,
            encrypt: parsedContainmentButtons[0].encrypt,
          },
        ],
        [
          {
            subType: parsedContainmentButtons[1].subType,
            encrypt: parsedContainmentButtons[1].encrypt,
          },
          {
            subType: parsedContainmentButtons[2].subType,
            encrypt: parsedContainmentButtons[2].encrypt,
          },
        ],
      ];
    } else {
      sortedTelegramMarkup = [
        [
          {
            subType: parsedContainmentButtons[0].subType,
            encrypt: parsedContainmentButtons[0].encrypt,
          },
          {
            subType: parsedContainmentButtons[1].subType,
            encrypt: parsedContainmentButtons[1].encrypt,
          },
        ],
      ];
    }
  }

  return sortedTelegramMarkup;
};

const sendNotification = (assetName: string) => {
  // notifying telegram users
  sendTelegramMessage({
    message: `[Auto contención] El equipo ${assetName} se contuvo automáticamente.`,
  });
  // notifying slack users
  // notifying app users
  // push app feed
  // notify to web users
  // push web feed
  return;
};
