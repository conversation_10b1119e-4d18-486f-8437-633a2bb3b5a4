import Axios from 'axios';

import { HttpError } from '@shared/models';
import { errors as conectionErrors } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

export const openRtrSession = async (
  assetId: string,
  url: string,
  token: string
): Promise<{ assetId: string; sessionId: string }> => {
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  const payload = {
    device_id: assetId,
  };

  try {
    const response = await Axios.post(
      url + 'real-time-response/entities/sessions/v1',
      payload,
      config
    );
    const crowdstrikeResponse = response.data as {
      meta: {};
      resources: Array<{ session_id: string; scripts: Array<{}> }>;
      errors: Array<{ code: number; message: string }>;
    };
    if (crowdstrikeResponse.resources.length > 0) {
      return {
        assetId: assetId,
        sessionId: crowdstrikeResponse.resources[0].session_id,
      };
    } else {
      if (crowdstrikeResponse.errors.length === 1) {
        if (crowdstrikeResponse.errors[0].message === 'Could not establish sensor comms') {
          throw conectionErrors.crowdstrike_device_connection(assetId);
        }
        throw conectionErrors.crowdstrike_invalid_device(assetId);
      }
      throw conectionErrors.crowdstrike_invalid_device(assetId);
    }
  } catch (err: any) {
    const csErrors = err.response.data.errors as {
      code: number;
      message: string;
    }[];
    if (csErrors.length === 1) {
      if (csErrors[0].message !== 'Could not establish sensor comms') {
        throw conectionErrors.crowdstrike_device_connection(assetId);
      }
    }
    throw conectionErrors.crowdstrike_invalid_device(assetId);
  }
};

export const executeCommand = async (
  command: string,
  assetData: { assetId: string; sessionId: string },
  url: string,
  token: string
): Promise<{ assetId: string; cloudRequestId: string }> => {
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  const payload = {
    command_string: command,
    session_id: assetData.sessionId,
  };
  try {
    const response = await Axios.post(
      url + 'real-time-response/entities/admin-command/v1',
      payload,
      config
    );
    const crowdstrikeResponse = response.data as {
      meta: {};
      resources: Array<{
        session_id: string;
        cloud_request_id: string;
        queued_command_offline: boolean;
      }>;
      errors: Array<{ code: number; message: string }>;
    };
    if (crowdstrikeResponse.resources.length > 0) {
      return {
        assetId: assetData.assetId,
        cloudRequestId: crowdstrikeResponse.resources[0].cloud_request_id,
      };
    } else {
      if (crowdstrikeResponse.errors.length > 0) {
        // notificar al usuario de error a traves de telegram
        throw new HttpError(
          `Equipo: ${assetData.assetId}, Error: ${crowdstrikeResponse.errors[0].message}`,
          crowdstrikeResponse.errors[0].code
        );
      }
      throw new HttpError(`Equipo: ${assetData.assetId}, Error: Inesperado`, 500);
    }
  } catch (err) {
    throw new HttpError(`Equipo: ${assetData.assetId}, Error: No se pudo ejecutar comando`, 500);
  }
};

export const getRtrResult = async (
  assetData: { assetId: string; cloudRequestId: string },
  url: string,
  token: string
): Promise<{
  complete: boolean;
  assetId: string;
  cloudRequestId: string;
  stdout: string;
  stderr: string;
}> => {
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  try {
    const response = await Axios.get(
      url +
        `real-time-response/entities/admin-command/v1?cloud_request_id=${assetData.cloudRequestId}&sequence_id=0`,
      config
    );
    const crowdstrikeResponse = response.data as {
      meta: {};
      resources: Array<{
        session_id: string;
        task_id: string;
        complete: boolean;
        stdout: string;
        stderr: string;
        base_command: string;
      }>;
      errors: Array<{ message: string; code: number }>;
    };
    if (crowdstrikeResponse.resources.length > 0) {
      const commandOutput = crowdstrikeResponse.resources[0];
      return {
        complete: commandOutput.complete,
        assetId: assetData.assetId,
        cloudRequestId: assetData.cloudRequestId,
        stdout: commandOutput.stdout,
        stderr: commandOutput.stderr,
      };
    } else {
      if (crowdstrikeResponse.errors.length > 0) {
        if (crowdstrikeResponse.errors.length === 1) {
          // notificar al usuario de error a traves de telegram
          throw new HttpError(
            `Equipo: ${assetData.assetId}, Error: ${crowdstrikeResponse.errors[0].message}`,
            crowdstrikeResponse.errors[0].code
          );
        } else {
          throw new HttpError(
            `Equipo: ${assetData.assetId}, Error: ${crowdstrikeResponse.errors.join(' | ')}`,
            503
          );
        }
      }
      throw new HttpError(`Equipo: ${assetData.assetId} Error: Inesperado`, 500);
    }
  } catch (err) {
    throw new HttpError(
      `Equipo: ${assetData.assetId}, Error: No se pudo obtener resultados de ejecución`,
      500
    );
  }
};

export const multipleRtrSession = async (
  assetsId: string[],
  command: string,
  url: string,
  token: string
) => {
  const errors: string[] = [];
  const sessionIdPromises = assetsId.map((assetId) => openRtrSession(assetId, url, token));
  let sessionIdsResolve: any[] = [];
  try {
    sessionIdsResolve = await Promise.allSettled(sessionIdPromises);
  } catch (err) {}

  if (sessionIdsResolve.length === 0) {
    throw conectionErrors.crowdstrike_communication_error();
  }

  // buscar por errores dentro de sessionId
  const cloudRequestIdPromises = sessionIdsResolve.map((sessionIdPromise) => {
    if (sessionIdPromise.status === 'fulfilled') {
      const response = sessionIdPromise.value as {
        assetId: string;
        sessionId: string;
      };
      return executeCommand(command, response, url, token);
    } else {
      errors.push(sessionIdPromise.reason.message);
      Logger.info(sessionIdPromise.reason.message);
      return Promise.reject(new Error(sessionIdPromise.reason.message));
    }
  });

  let cloudRequestIds: any[] = [];
  try {
    const responses = await Promise.allSettled(cloudRequestIdPromises);
    cloudRequestIds = responses.map((cloudRequestId) => {
      if (cloudRequestId.status === 'fulfilled') {
        const response = cloudRequestId.value as {
          assetId: string;
          cloudRequestId: string;
        };
        return response;
      } else {
        if (!errors.includes(cloudRequestId.reason.message)) {
          errors.push(cloudRequestId.reason.message);
          Logger.info(`[RTR Group Error] ${cloudRequestId.reason.message}`);
        }
      }
    });
  } catch (err) {
    errors.push('No se pudo establecer conexión con los equipos.');
  }

  if (cloudRequestIds.length === 0) {
    return false;
  }

  const cloudRequestIdsFiltered: { assetId: string; cloudRequestId: string }[] =
    cloudRequestIds.filter((item) => item && item);

  return { cloudRequestIds: cloudRequestIdsFiltered, errors };
};

export const multipleRtrExecute = async (
  assetsData: { assetId: string; cloudRequestId: string }[],
  url: string,
  token: string
) => {
  // ahora que tengo consola y bearer token busco resultados
  const errors: string[] = [];
  const remaning: string[] = [];
  const rtrResults = assetsData.map((assetData) => getRtrResult(assetData, url, token));

  let rtrResponses: any[] = [];
  try {
    const responses = await Promise.allSettled(rtrResults);
    rtrResponses = responses.map((rtrResult) => {
      if (rtrResult.status === 'fulfilled') {
        // tengo respuesta complete de rtr
        if (rtrResult.value.complete) {
          const { assetId, stdout, stderr } = rtrResult.value;
          if (stderr.length !== 0) {
            errors.push(`Equipo: ${assetId}, Error: ${stderr}\n`);
            Logger.info(`[RTR Group Error] Equipo: ${assetId}, Error: ${stderr}`);
          } else {
            Logger.info(`[RTR Group] Equipo: ${assetId}`);
            return { assetId, stdout };
          }
        } else {
          const { assetId } = rtrResult.value;
          remaning.push(assetId);
        }
      } else {
        if (!errors.includes(rtrResult.reason.message)) {
          errors.push(rtrResult.reason.message);
          Logger.info(`[RTR Group Error] ${rtrResult.reason.message}`);
        }
      }
    });
  } catch (err) {
    errors.push('No se pudo establecer conexión con los equipos.');
  }

  const rtrResponsesFiltered: {
    assetId: string;
    stdout: string;
  }[] = rtrResponses.filter((item) => item && item);

  return { responses: rtrResponsesFiltered, errors, remaning };
};

export const getFormattedStdout = (response: { [key: string]: any }) => {
  const keys = Object.keys(response);
  let responseText: string = '';
  for (const key of keys) {
    responseText += `${key}: ${response[key]}\n`;
  }
  return responseText;
};

export const getFormattedResponse = (result: {
  complete: boolean;
  assetId: string;
  cloudRequestId: string;
  stdout: string;
  stderr: string;
}) => {
  if (result.stdout.length !== 0) {
    const stdoutFormatted = result.stdout;
    return `[Stdout]: ${stdoutFormatted}`;
  }

  if (result.stderr.length !== 0) {
    return `[Stderr]: ${result.stderr}`;
  }

  return '';
};
