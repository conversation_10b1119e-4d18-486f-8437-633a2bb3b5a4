import { Types } from 'mongoose';

import { CSAdAction } from '../../../schemas';
import { Ad } from '@shared/schemas/ad.schema';

import { openRtrSession, executeCommand, getRtrResult, getAuthInfo, getFormattedStdout } from '..';

import { HttpError } from '@shared/models';
import { isAsociate } from '@shared/helpers/functions/ad-validations.function';

// funcion para ejecutar el rtr contra el ad
export const executeRTRToAD = async (
  adId: Types.ObjectId,
  CSAdActionId: Types.ObjectId,
  user: string,
  inputValue?: string
) => {
  // validar que exista el asset
  const existingAd = await Ad.findById(adId);
  if (!existingAd) {
    throw new HttpError('Este equipo seleccionado no existe', 404);
  }
  // validar que el ad este asociado a crowdstrike
  if (!isAsociate(existingAd, 'cs')) {
    throw new HttpError(
      'El equipo seleccionado no esta asociado a ninguna consola de CrowdStrike',
      404
    );
  }
  // validar que exista el action
  const existingAction = await CSAdAction.findById(CSAdActionId);
  if (!existingAction) {
    throw new HttpError('La accion seleccionada no existe', 404);
  }
  const consoleConfig = existingAd.crowdStrikeConfig;
  const { token, url } = await getAuthInfo(consoleConfig.consoleId);

  const formattedCommand = getFormattedCommand(existingAction.command, user, inputValue);
  // abrir sesion de rtr
  const rtrSessionId = await openRtrSession(consoleConfig.assetId, url, token);

  const cloudRequestId = await executeCommand(formattedCommand, rtrSessionId, url, token);

  return cloudRequestId;
};

// funcion para obtener resultados de ejecucion
export const getRtrResults = async (
  cloudRequestId: {
    assetId: string;
    cloudRequestId: string;
  },
  consoleId: Types.ObjectId
) => {
  const { token, url } = await getAuthInfo(consoleId);
  const result = await getRtrResult(cloudRequestId, url, token);

  if (!result.complete) {
    return false;
  }

  if (result.stdout.length !== 0) {
    const stdoutFormatted = getFormattedStdout(JSON.parse(result.stdout));
    return `[Stdout]: ${stdoutFormatted}`;
  }

  if (result.stderr.length !== 0) {
    return `[Stderr]: ${result.stderr}`;
  }

  return '';
};

// esta funcion combina el comando que se haya introducido con el input del usuario para ejecutar el comando sobre powershell
const getFormattedCommand = (action: string, user: string, input?: string) => {
  // los templates dentro de las action van a estar definidos por |||
  let formattedAction = action;
  if (input) {
    formattedAction = formattedAction.replace('Input-Value', input);
  }
  formattedAction = formattedAction.replace('Input-User', user);
  return formattedAction;
};
