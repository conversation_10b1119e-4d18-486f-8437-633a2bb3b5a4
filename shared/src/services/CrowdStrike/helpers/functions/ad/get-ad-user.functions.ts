import { Types } from 'mongoose';

import { openRtrSession, executeCommand, getRtrResult, getAuthInfo } from '..';

import { errors } from '@shared/utils/app-errors';

export const searchUsersInAd = async (
  input: string,
  type: 'user' | 'host',
  assetId: string,
  consoleId: Types.ObjectId
) => {
  const { token, url } = await getAuthInfo(consoleId);

  // abrir sesion de rtr
  let rtrSessionId: { assetId: string; sessionId: string };
  try {
    rtrSessionId = await openRtrSession(assetId, url, token);
  } catch (err) {
    throw err;
  }

  let command: string;
  if (type === 'user') {
    command = `runscript -Raw=\`\`\`Get-ADUser -Filter "UserPrincipalName -like '*${input}*'" | ConvertTo-Json\`\`\``;
  } else if (type === 'host') {
    command = `runscript -Raw=\`\`\`Get-ADComputer -Filter "name -like '*${input}*'" | ConvertTo-<PERSON><PERSON>\`\`\``;
  } else {
    throw errors.type_not_valid();
  }
  let cloudRequestId: { assetId: string; cloudRequestId: string };
  try {
    cloudRequestId = await executeCommand(command, rtrSessionId, url, token);
  } catch (err) {
    throw errors.crowdstrike_execution_error();
  }

  return cloudRequestId;
};

export const getSearchResult = async (
  cloudRequestId: {
    assetId: string;
    cloudRequestId: string;
  },
  consoleId: Types.ObjectId,
  type: 'user' | 'host'
) => {
  const { token, url } = await getAuthInfo(consoleId);
  const result = await getRtrResult(cloudRequestId, url, token);

  if (!result.complete) {
    return false;
  }

  if (result.stdout.length === 0) {
    return [];
  }

  if (type === 'user') {
    const users = JSON.parse(result.stdout);
    const parsedUsers: {
      UserPrincipalName: string;
    }[] = [];
    if (Array.isArray(users)) {
      parsedUsers.push(...users);
    } else {
      parsedUsers.push(users);
    }
    const filteredUsersNames: string[] = [];

    filteredUsersNames.push(...parsedUsers.map((u) => u.UserPrincipalName));

    return filteredUsersNames;
  } else if (type === 'host') {
    const hosts = JSON.parse(result.stdout);
    const parsedHosts: {
      Name: string;
    }[] = [];
    if (Array.isArray(hosts)) {
      parsedHosts.push(...hosts);
    } else {
      parsedHosts.push(hosts);
    }
    const filteredHost: string[] = [];

    filteredHost.push(...parsedHosts.map((h) => h.Name));

    return filteredHost;
  } else {
    //this never should happend
    return [];
  }
};
