import Axios from 'axios';

import { handlerCsErrors } from '../utils/crowdstrike-errors';

export const getSoList = async (
  platform: string,
  url: string,
  token: string
): Promise<string[]> => {
  const payload = [
    {
      type: 'terms',
      field: 'device.os_version',
      size: 10,
      min_doc_count: 1,
      name: 'find',
      filter: `device.platform_name:'${platform}'+device.os_version:*'*'`,
    },
  ];
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  try {
    const response = (await Axios.post(
      url + `detects/aggregates/detects/GET/v1`,
      payload,
      config
    )) as {
      data: {
        meta: {};
        resources: Array<{
          name: string;
          buckets: Array<{ label: string; count: number }>;
        }>;
      };
    };
    const resources = response.data.resources;

    if (resources.length === 0 || !resources[0].buckets) {
      return [];
    }

    const csBucket = resources[0].buckets;
    const listOfSo: string[] = [];
    if (csBucket.length !== 0) {
      for (const item of csBucket) {
        listOfSo.push(item.label);
      }
    }
    return listOfSo;
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};
