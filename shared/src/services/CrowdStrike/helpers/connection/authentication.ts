import Axios from 'axios';

import { errors } from '@shared/utils/app-errors';

export const getCrowdStrikeToken = async (
  url: string,
  clientId: string,
  secretId: string
): Promise<string> => {
  const config = {
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  };
  const params = new URLSearchParams();
  params.append('client_id', clientId);
  params.append('client_secret', secretId);
  try {
    const updatedToken = await Axios.post(url + 'oauth2/token', params, config);
    const crowdstrikeResponse = updatedToken.data as {
      access_token: string;
      expires_in: number;
      token_type: string;
    };
    return crowdstrikeResponse.access_token;
  } catch (err) {
    throw errors.crowdstrike_token_error();
  }
};
