// Dependencies
import Axios from 'axios';

// Models
import { groupFilters } from '@shared/services/Bot/helpers/models';

// Helpers
import { handlerCsErrors } from '../utils/crowdstrike-errors';
import { errors } from '@shared/utils/app-errors';
import { Logger } from '@shared/helpers/classes/logger.class';

/**
 * Get devices by name
 * @param name
 * @param token
 * @returns {resource: string[], errors: {code: number, message: string}[]}
 */
export const getDevicesIds = async (name: string, url: string, token: string) => {
  const config = getHeader(token);
  try {
    const response = await Axios.get(
      url + `devices/queries/devices/v1?limit=100&filter=hostname:'${name}'`,
      config
    );

    return response.data as {
      resources: string[];
      errors: { code: number; message: string }[];
    };
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};
/**
 * Get info from device id
 * @param ids
 * @param token
 * @returns
 */
export const getDevicesDetails = async (ids: string[], url: string, token: string) => {
  const config = getHeader(token);
  try {
    const response = await Axios.get(url + `devices/entities/devices/v2${getQuery(ids)}`, config);

    return response.data as {
      resources: {
        device_id: string;
        hostname: string;
        platform_name: string;
        os_version: string;
        tags: string[];
      }[];
      errors: { code: number; message: string }[];
    };
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

export const getAssetIds = async (payload: groupFilters, url: string, token: string) => {
  const query: { [key: string]: string | number } = {
    filter: "last_seen:>='2016-07-04'",
    offset: 0,
    limit: 4900,
  };

  // agregamos los filtros que se encuentren en tempData
  if (payload.platform) {
    query.filter = `${query.filter}%2Bplatform_name:'${payload.platform}'`;
  }
  if (payload.os_version) {
    query.filter = `${query.filter}%2Bos_version:'${payload.os_version}'`;
  }
  if (payload.workstation) {
    query.filter = `${query.filter}%2Bproduct_type_desc:'Workstation'`;
  }
  if (payload.server) {
    query.filter = `${query.filter}%2Bproduct_type_desc:'Server'`;
  }
  if (payload.group_id) {
    query.id = payload.group_id;
  }

  const config = getHeader(token);

  let queryStringyfied = '';

  for (let i = 0; i < Object.keys(query).length; i++) {
    const param = Object.keys(query)[i];
    queryStringyfied = `${queryStringyfied}${i > 0 ? '&' : ''}${param}=${query[param]}`;
  }

  try {
    const response = (await Axios.get(
      url + `devices/queries/host-group-members/v1?${queryStringyfied}`,
      config
    )) as { data: { resources: string[] } };
    return response.data.resources;
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

export const getDefinedGroups = async (url: string, token: string) => {
  const config = getHeader(token);
  try {
    const response = (await Axios.get(url + `devices/combined/host-groups/v1`, config)) as {
      data: {
        meta: {};
        resources: Array<{
          id: string;
          name: string;
        }>;
      };
    };
    const resources = response.data.resources;

    if (resources.length === 0) {
      return [];
    }
    const groupList: { id: string; name: string }[] = [];
    if (resources.length !== 0) {
      for (const group of resources) {
        groupList.push(group);
      }
    }
    return groupList;
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

const getQuery = (ids: string[]) => {
  let url: string = '?';
  for (const [index, value] of ids.entries()) {
    if (index === ids.length - 1) {
      url = url + `ids=${value}`;
    } else {
      url = url + `ids=${value}` + '&';
    }
  }
  return url;
};

export const getDeviceFromAgentId = async (agentId: string, url: string, token: string) => {
  const details = await getDevicesDetails([agentId], url, token);
  if (details.resources.length === 0) {
    throw errors.crowdstrike_device_not_found();
  }
  return details.resources[0];
};

export const removeDeviceFromCrowdstrike = async (agentId: string, url: string, token: string) => {
  const config = getHeader(token);
  try {
    await Axios.post(
      url + 'devices/entities/devices-actions/v2?action_name=hide_host',
      { ids: [agentId] },
      config
    );

    return true;
  } catch (err: any) {
    Logger.error(`Error when trying to remove device from CrowdStrike: ${err}`);
  }
};

export const getDevicesFromTag = async (tag: string, url: string, token: string) => {
  const config = getHeader(token);
  try {
    const response = await Axios.get(
      url + `devices/queries/devices/v1?limit=1&sort=last_seen.desc&filter=tags:'${tag}'`,
      config
    );

    return response.data as {
      resources: string[];
      errors: { code: number; message: string }[];
    };
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

const getHeader = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
};
