import Axios from 'axios';

// This function push
export const setIOC = async (
  creator: string,
  action: string,
  severity: string | undefined,
  type: string,
  platform: string,
  tags: string[],
  description: string,
  entries: string[],
  url: string,
  token: string
) => {
  // Header
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  // Building payload
  const indicators: any[] = [];
  for (const entry of entries) {
    indicators.push({
      type: type.toLowerCase(),
      value: entry,
      action: action.toLowerCase(),
      severity: severity ? severity.toLowerCase() : undefined,
      tags: tags,
      description: description,
      metadata: {
        signed: false,
        av_hits: -1,
      },
      platforms: [platform],
      expired: false,
      deleted: false,
      applied_globally: true,
      created_by: '<EMAIL>',
    });
  }

  try {
    const response = await Axios.post(
      url + `iocs/entities/indicators/v1?ignore_warnings=false`,
      { indicators: indicators },
      config
    );
    const data = response.data as {
      resources: { id: string; path: string }[];
      errors: {
        code: number;
        message: string;
      }[];
    };
    const resources = data.resources ?? [];
    const iocErrors = data.errors ?? [];
    return { resources, iocErrors };
  } catch (err: any) {
    const errors =
      (err.response.data.errors as {
        code: number;
        message: string;
      }[]) ?? [];
    const resources =
      (err.response.data.resources as {
        id: string;
        path: string;
      }[]) ?? [];
    return { resources, iocErrors: errors };
  }
};
