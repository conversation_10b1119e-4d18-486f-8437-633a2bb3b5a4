import Axios from 'axios';

export const setContainStatus = async (
  asset: string | string[],
  type: 'contain' | 'lift_containment',
  url: string,
  token: string
) => {
  const config = {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  };
  const listOfAssets: string[] = [];
  if (typeof asset === 'string') {
    listOfAssets.push(asset);
  } else {
    listOfAssets.push(...asset);
  }
  const payload = {
    ids: listOfAssets,
  };

  try {
    const response = await Axios.post(
      url + `devices/entities/devices-actions/v2?action_name=${type}`,
      payload,
      config
    );

    return response.data as {
      resources: { id: string; path: string }[];
      errors: {
        code: number;
        message: string;
      }[];
    };
  } catch (err: any) {
    const errors = err.response.data.errors as {
      code: number;
      message: string;
    }[];
    const resources = err.response.data.resources as {
      id: string;
      path: string;
    }[];
    return { resources, errors };
  }
};
