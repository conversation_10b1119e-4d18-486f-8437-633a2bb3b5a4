import Axios from 'axios';

import { handlerCsErrors } from '../utils/crowdstrike-errors';
import { errors } from '@shared/utils/app-errors';

export const openRtrSession = async (assetId: string, url: string, token: string) => {
  const config = {
    headers: {
      Authorization: `Bear<PERSON> ${token}`,
    },
  };
  const payload = {
    device_id: assetId,
  };

  try {
    const response = await Axios.post(
      url + 'real-time-response/entities/sessions/v1',
      payload,
      config
    );
    const data = response.data as {
      meta: {};
      resources: { session_id: string; scripts: {}[] }[];
      errors: { code: number; message: string }[];
    };
    if (data.resources.length === 0) {
      // no hay session id
      throw errors.crowdstrike_custom_error(data.errors[0].message, data.errors[0].code);
    }
    return data.resources[0].session_id;
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

export const useCommand = async (command: string, session: string, url: string, token: string) => {
  const config = { headers: { Authorization: `Bear<PERSON> ${token}` } };
  const payload = { command_string: command, session_id: session };
  try {
    const response = await Axios.post(
      url + 'real-time-response/entities/admin-command/v1',
      payload,
      config
    );
    const data = response.data as {
      meta: {};
      resources: {
        session_id: string;
        cloud_request_id: string;
        queued_command_offline: boolean;
      }[];
      errors: { code: number; message: string }[];
    };
    if (data.resources.length === 0) {
      // no hay session id
      throw errors.crowdstrike_custom_error(data.errors[0].message, data.errors[0].code);
    }
    return data.resources[0].cloud_request_id;
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};

export const getRtrResult = async (cloudRequestId: string, url: string, token: string) => {
  const config = { headers: { Authorization: `Bearer ${token}` } };
  try {
    const response = await Axios.get(
      url +
        `real-time-response/entities/admin-command/v1?cloud_request_id=${cloudRequestId}&sequence_id=0`,
      config
    );
    const data = response.data as {
      meta: {};
      resources: {
        session_id: string;
        task_id: string;
        complete: boolean;
        stdout: string;
        stderr: string;
        base_command: string;
      }[];
      errors: { message: string; code: number }[];
    };
    if (data.resources.length === 0) {
      // no hay session id
      throw errors.crowdstrike_custom_error(data.errors[0].message, data.errors[0].code);
    }
    return data.resources[0];
  } catch (err: any) {
    throw handlerCsErrors(err);
  }
};
