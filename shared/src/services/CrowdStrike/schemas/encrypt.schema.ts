import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { EncryptActionModel } from '../helpers/models';

const encryptSchema = schemaFactory({
  assetId: { type: String, required: true },
  assetName: { type: String, required: true },
  detectionId: { type: String, required: true },
  action: { type: Types.ObjectId, required: true, ref: 'Action' },
  expire: { type: Date, required: true },
});

export type EncryptDocument = (EncryptActionModel & Document & { _id: Types.ObjectId }) | null;
export const Encrypt = model<EncryptActionModel>('Encrypt', encryptSchema);
