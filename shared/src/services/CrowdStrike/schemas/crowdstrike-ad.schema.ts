import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { CSAdActionModel } from '../helpers/models';

const csAdActionSchema = schemaFactory({
  ad: [{ type: Types.ObjectId, required: true, ref: 'Ad' }],
  label: { type: String, required: true },
  command: { type: String, required: true },
  retryTime: { type: Number, required: true },
  type: { type: String, required: true, default: 'Usuario' },
  order: { type: Number, required: true },
  needsInput: { type: Boolean, required: true, default: false },
  inputText: { type: String },
});

export type CSAdActionDocument = (CSAdActionModel & Document & { _id: Types.ObjectId }) | null;
export const CSAdAction = model<CSAdActionModel>('CSAdAction', csAdActionSchema);
