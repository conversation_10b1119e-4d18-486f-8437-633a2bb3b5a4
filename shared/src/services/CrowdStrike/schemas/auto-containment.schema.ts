import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { AutoContainmentModel } from '../helpers/models';

const autoContainmentSchema = schemaFactory({
  console: { type: Types.ObjectId, required: true, ref: 'Console' },
  assetId: { type: String, required: true },
  assetName: { type: String, required: true },
  trigger: { type: Boolean, required: true },
  time: { type: Date, required: true },
});

autoContainmentSchema.index({ console: 1 });
autoContainmentSchema.index({ assetId: 1 });

export type AutoContainmentDocument =
  | (AutoContainmentModel & Document & { _id: Types.ObjectId })
  | null;
export const AutoContainment = model<AutoContainmentModel>(
  'AutoContainment',
  autoContainmentSchema
);
