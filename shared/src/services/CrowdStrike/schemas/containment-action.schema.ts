import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { ContainmentActionModel } from '../helpers/models';

const containmentButtonSchema = schemaFactory({
  type: { type: String, required: true },
  consoleId: { type: Types.ObjectId, required: true, ref: 'Console' },
  assetId: { type: String, required: true },
  assetName: { type: String, required: true },
  expire: { type: Date, required: true },
});

containmentButtonSchema.index({ type: 1 });
containmentButtonSchema.index({ consoleId: 1 });
containmentButtonSchema.index({ expire: 1 });

export type ContainmentButtonDocument =
  | (ContainmentActionModel & Document & { _id: Types.ObjectId })
  | null;
export const ContainmentButton = model<ContainmentActionModel>(
  'ContainmentButton',
  containmentButtonSchema
);
