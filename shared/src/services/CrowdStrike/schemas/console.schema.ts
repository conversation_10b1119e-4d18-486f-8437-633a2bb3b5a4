import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { ConsoleModel } from '../helpers/models';

const consoleSchema = schemaFactory({
  name: { type: String, required: true },
  apiUrl: {
    type: String,
    required: true,
    default: 'https://api.crowdstrike.com/',
  },
  clientId: { type: String, required: true },
  secretId: { type: String, required: true },
  token: { type: String, required: true },
  licenses: { type: Number, required: true, default: 500 },
  autoContainmentTime: { type: Number, required: true, default: 3600 },
  expiringButtonTime: { type: Number, required: true, default: 3600 },
  activeSeverities: {
    type: [String],
    required: true,
    default: ['Critical', 'High'],
  },
  inactiveSeverities: {
    type: [String],
    required: true,
    default: ['Critical'],
  },
  activeAutoContainment: { type: Boolean, required: true, default: false },
  activeManualContainment: { type: Boolean, required: true, default: true },
  inactiveAutoContainment: { type: Boolean, required: true, default: true },
  inactiveManualContainment: {
    type: Boolean,
    required: true,
    default: false,
  },
  activeActions: { type: Boolean, required: true, default: true },
  inactiveActions: { type: Boolean, required: true, default: true },
});

export type ConsoleDocument = (ConsoleModel & Document & { _id: Types.ObjectId }) | null;
export const Console = model<ConsoleModel>('Console', consoleSchema);
