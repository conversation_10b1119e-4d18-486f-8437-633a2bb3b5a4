import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { ActionModel } from '../helpers/models';

const actionSchema = schemaFactory({
  console: { type: Types.ObjectId, ref: 'Console' },
  label: { type: String, required: true },
  command: { type: String, required: true },
  retryTime: { type: Number, required: true },
  platform: { type: String, required: true },
  so: [{ type: String, required: true }],
  order: { type: Number, required: true },
});

export type ActionDocument = (ActionModel & Document & { _id: Types.ObjectId }) | null;
export const Action = model<ActionModel>('Action', actionSchema);
