import { Document, Schema, model, Types } from 'mongoose';

import { DetectionModel } from '../helpers/models';

const detectionSchema = new Schema({
  data: { type: Object, required: true },
  meta: { type: Object, required: true },
  detectionId: { type: String, required: true },
  console: { type: Types.ObjectId, required: true, ref: 'Console' },
});

detectionSchema.index({ detectionId: 1 });

export type DetectionDocument = (DetectionModel & Document & { _id: Types.ObjectId }) | null;
export const Detection = model<DetectionModel>('Detection', detectionSchema);
