import { STATUS } from './inventory';

export const STATUS_HOSTS_DETAILS_PIPELINE = [
  { $unwind: '$policyReport' },
  { $replaceRoot: { newRoot: '$policyReport' } },
  {
    $lookup: {
      from: 'rportclients',
      localField: 'rportId',
      foreignField: 'rportId',
      as: 'rportClient',
    },
  },
  {
    $addFields: {
      name: {
        $cond: {
          if: { $gt: [{ $size: '$rportClient' }, 0] },
          then: { $arrayElemAt: ['$rportClient.name', 0] },
          else: 'NOT_FOUND',
        },
      },
    },
  },
  { $project: { rportClient: 0 } },
];

export const getStatusHostsDetailsPipeline = (withWithoutInventoryHosts: boolean = false) => {
  const basePipeline: any[] = [
    { $unwind: '$policyReport' },
    { $replaceRoot: { newRoot: '$policyReport' } },
    { $set: { rportId: '$rportId' } },
  ];

  if (withWithoutInventoryHosts) {
    basePipeline.push({
      $addFields: {
        policies: {
          $cond: {
            if: { $eq: ['$status', STATUS.WITHOUT_INVENTORY] },
            then: [
              {
                name: STATUS.WITHOUT_INVENTORY,
              },
            ],
            else: '$policies',
          },
        },
      },
    });
  }

  return [
    ...basePipeline,
    { $unwind: '$policies' },
    {
      $replaceRoot: {
        newRoot: {
          compliancePercentage: '$compliancePercentage',
          lastVerifiedAt: '$lastVerifiedAt',
          rportId: '$rportId',
          rules: '$rules',
          status: '$status',
          automaticUninstall: '$policies.automaticUninstall',
          id: '$policies._id',
          mustBePresent: '$policies.mustBePresent',
          name: '$policies.name',
          compliant: '$policies.compliant',
          vendor: '$policies.vendor',
          versions: '$policies.versions',
          deleted: false,
        },
      },
    },
    {
      $lookup: {
        from: 'rportclients',
        localField: 'rportId',
        foreignField: 'rportId',
        as: 'rportClient',
      },
    },
    {
      $addFields: {
        hostname: {
          $cond: {
            if: { $gt: [{ $size: '$rportClient' }, 0] },
            then: { $arrayElemAt: ['$rportClient.name', 0] },
            else: 'NOT_FOUND',
          },
        },
      },
    },
    { $project: { rportClient: 0 } },
  ];
};
