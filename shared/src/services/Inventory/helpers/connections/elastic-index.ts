import { Client } from '@elastic/elasticsearch';

import { errors } from '@shared/utils/app-errors';

import { ELASTIC_BASE_URL, ELASTIC_API_KEY, ELASTIC_INVENTORY_INDEX } from '@shared/constants/env';

let elasticClient: undefined | Client = undefined;

if (ELASTIC_BASE_URL && ELASTIC_API_KEY && ELASTIC_INVENTORY_INDEX) {
  elasticClient = new Client({
    node: ELASTIC_BASE_URL,
    auth: {
      apiKey: ELASTIC_API_KEY,
    },
  });
}

// Utility function to check if an index exists
export const indexExists = async (index: string): Promise<boolean> => {
  if (!elasticClient)
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  const exists = await elasticClient.indices.exists({ index });
  return exists;
};

// Utility function to create an index with a specific mapping
export const createIndex = async (index: string, body: object) => {
  if (!elasticClient)
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  await elasticClient.indices.create({ index, body });
};

// Utility function to move data between indices
export const reindex = async (source: string, dest: string) => {
  if (!elasticClient)
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  await elasticClient.reindex({
    body: {
      source: { index: source },
      dest: { index: dest },
    },
    wait_for_completion: true,
  });
};

// Utility function to delete an index
export const deleteIndex = async (index: string) => {
  if (!elasticClient)
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  await elasticClient.indices.delete({ index });
};
