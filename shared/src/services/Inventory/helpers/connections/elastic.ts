import axios, { AxiosInstance } from 'axios';

import { Client } from '@elastic/elasticsearch';
import { SortCombinations } from '@elastic/elasticsearch/lib/api/types';

import { SearchParams } from '../models/elastic.model';
import { AppUniqueValuesAggregation } from '../models';

import { Logger } from '@shared/helpers/classes/logger.class';
import { errors } from '@shared/utils/app-errors';

import { MAX_KEYWORD_ON_SEARCH } from '../constants';

import { ELASTIC_BASE_URL, ELASTIC_API_KEY, ELASTIC_INVENTORY_INDEX } from '@shared/constants/env';

let elasticDirectlyClient: undefined | AxiosInstance = undefined;
let elasticClient: undefined | Client = undefined;

if (ELASTIC_BASE_URL && ELASTIC_API_KEY && ELASTIC_INVENTORY_INDEX) {
  elasticClient = new Client({
    node: ELASTIC_BASE_URL,
    auth: {
      apiKey: ELASTIC_API_KEY,
    },
  });

  elasticDirectlyClient = axios.create({
    baseURL: ELASTIC_BASE_URL,
    headers: { Authorization: `ApiKey ${ELASTIC_API_KEY}` },
  });
}

export const getElasticDocuments = async (
  params: SearchParams = {
    index: ELASTIC_INVENTORY_INDEX!,
    columns: [],
    limit: 100,
    offset: 0,
    query: {},
    sortField: '_id',
    sortOrder: 'asc',
  }
) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }
  const { index, sortField, sortOrder, offset, limit, columns, query } = params;

  const searchParams: { [key: string]: any } = {
    index,
    from: offset,
    size: limit,
    track_total_hits: true,
    body: {
      _source: columns,
      query: {
        match_all: {},
      },
    },
  };

  // If sortField and SortOrder are provided add to query object
  if (sortField && sortOrder) {
    searchParams.body.sort = [{ [sortField]: { order: sortOrder } }];
  }

  if (query) {
    searchParams.body.query = query;
  }

  try {
    const response = await elasticClient.search(searchParams);

    if (!response.hits || !response.hits.hits) {
      return { data: [], meta: { count: 0, resources: 0, offset } };
    }

    const count =
      typeof response.hits.total === 'object' ? response.hits.total.value : response.hits.total;

    return {
      data: response.hits.hits,
      meta: {
        count: count || response.hits.hits.length,
        resources: response.hits.hits.length,
        offset: +(offset || 0),
      },
    };
  } catch (error) {
    throw error;
  }
};

export const runElasticRawQuery = async (query: Record<string, any>) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    const response = await elasticClient.search({
      index: ELASTIC_INVENTORY_INDEX,
      body: query,
    });

    return response;
  } catch (error: any) {
    Logger.error('Unable to create host in Elastic:', error.meta);
    throw error;
  }
};

export const runElasticSearchQuery = async (
  _source: string[] | boolean,
  from: number | null = 0,
  size: number | null = 25,
  query: Record<string, any | Record<string, any>> = { match_all: {} },
  sort: SortCombinations[] = [{}],
  extraBody: Record<string, any> = {}
) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    const response = await elasticClient.search({
      index: ELASTIC_INVENTORY_INDEX,
      body: {
        track_total_hits: true,
        _source,
        ...(from !== null && { from }),
        ...(size !== null && { size }),
        query,
        sort,
        ...extraBody,
      },
    });

    return response;
  } catch (error: any) {
    Logger.error('Unable to create host in Elastic:', error.meta);
    throw error;
  }
};

export const createHostDataInElastic = async (hostId: string) => {
  if (elasticDirectlyClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    await elasticDirectlyClient.post(`/${ELASTIC_INVENTORY_INDEX}/_create/${hostId}`, {
      Applications: [],
      ApplicationsTimestamp: new Date(),
      Services: [],
      ServicesTimestamp: new Date(),
      hostId: hostId,
    });
    Logger.info(`Host ${hostId} successfully created in Elastic.`);
    return true;
  } catch (error: any) {
    if (error.response.status === 409) {
      // Host already exists in Elastic.
      return true;
    } else {
      throw error;
    }
  }
};

const _getProperties = (mapping: any): Record<string, string> => {
  const properties: Record<string, string> = {};

  const exploreProperties = (obj: any, path = ''): void => {
    Object.entries(obj).forEach(([name, value]: [string, any]) => {
      const newPath = path ? `${path}.${name}` : name;

      if (value.properties) {
        exploreProperties(value.properties, newPath);
      } else {
        properties[newPath] = value.type;
      }
    });
  };

  exploreProperties(mapping);
  return properties;
};

export const getAvailableFields = async () => {
  if (elasticClient === undefined || ELASTIC_INVENTORY_INDEX === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    const response = await elasticClient.indices.getMapping({ index: ELASTIC_INVENTORY_INDEX });
    const indexMapping = (response as any)[ELASTIC_INVENTORY_INDEX].mappings.properties;
    const properties = _getProperties(indexMapping);

    return properties;
  } catch (error) {
    throw error;
  }
};

export const getUniqueFieldValues = async (
  field: string,
  text: string = '',
  limit: number = MAX_KEYWORD_ON_SEARCH
) => {
  if (elasticClient === undefined || ELASTIC_INVENTORY_INDEX === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    const response = await elasticClient.search({
      index: ELASTIC_INVENTORY_INDEX,
      size: 0,
      body: {
        aggs: {
          nested_applications: {
            nested: {
              path: 'Applications',
            },
            aggs: {
              unique_values: {
                terms: {
                  field: `${field}.keyword`,
                  size: limit,
                  include: `.*${text}.*`,
                },
              },
            },
          },
        },
      },
    });

    if (!response.aggregations) {
      return [];
    }

    const aggregation = response.aggregations.nested_applications as AppUniqueValuesAggregation;

    const uniqueValues = aggregation.unique_values.buckets
      .filter((bucket) => bucket.key.trim().length > 0)
      .map((bucket) => ({
        key: bucket.key,
        value: bucket.key,
      }));

    return uniqueValues;
  } catch (error) {
    throw error;
  }
};

export const getIndex = async (index: string) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    return await elasticClient.indices.exists({ index });
  } catch (error) {
    throw error;
  }
};

export const createIndex = async (name: string, body: { [key: string]: any }) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    return await elasticClient.indices.create({ index: name, body });
  } catch (error) {
    throw error;
  }
};

export const deleteDocument = async (index: string, id: string) => {
  if (elasticClient === undefined) {
    throw errors.environment_variable_not_set(
      'ELASTIC_BASE_URL, ELASTIC_API_KEY or ELASTIC_INVENTORY_INDEX'
    );
  }

  try {
    return await elasticClient.delete({ index: index, id: id });
  } catch (error) {
    throw error;
  }
};
