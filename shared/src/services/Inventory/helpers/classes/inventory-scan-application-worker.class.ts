import AbstractWorker from '@shared/services/Queues/helpers/worker/abstractWorker';
import TasksFunctions from '@shared/services/Queues/helpers/functions/task.function';

import {
  ApplicationPolicy as ApplicationPolicySchema,
  ApplicationPolicyDocument,
} from '@shared/services/Inventory/schemas/application_policy.schema';

import { errors } from '@shared/utils/app-errors';
import { Task } from '@shared/services/Queues/helpers/types';
import { User } from '@shared/schemas';
import { ApplicationPolicyStatus } from '../../schemas';
import { ApplicationPolicyStatusDocument } from '../../schemas/application_policy_status.schema';
import { RportGroup } from '../../../Rport/schemas';
import { QueuesTask } from '@shared/services/Queues/schemas';

import { applicationPolicySwitch, getAndUpdateNextDate } from '../utils/application_policy.utils';
import {
  getExportedColumns,
  notifyReport,
  updateNamesToPolicyReport,
} from '../utils/application_policy_report.utils';

import { generateExportFileOf } from '@shared/services/Export/controllers/export.controllers';
import { Column } from '@shared/services/Export/helpers/interfaces';
import { CANCELLED, FINISHED, PENDING } from '@shared/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { KnownQueuesTaskDocument } from '@shared/services/Queues/schemas/task.schema';
import { HostsReportModel } from '../models';
import { getAllApplicationPolicyStatus } from '../functions/control_application.functions';

export class InventoryScanApplicationPolicyWorker extends AbstractWorker<Task<TASK_NAMES.NOTIFY>> {
  inventoryApplicationPolicy: ApplicationPolicyDocument | null = null;
  inventoryApplicationPolicyStatus: ApplicationPolicyStatusDocument | null = null;
  clientIds: string[] = [];

  constructor(task: Task<TASK_NAMES.NOTIFY>) {
    super(task);
  }

  private async createNewTask() {
    const newTask = await TasksFunctions.createTask(
      this.task.name,
      this.task.priority,
      this.task.params,
      { author: this.task.author }
    );

    const nextDate = await getAndUpdateNextDate(
      this.inventoryApplicationPolicy?.periodicity as number,
      'lastNotify',
      'nextNotify',
      this.inventoryApplicationPolicy?._id,
      this.task.retryAfter || new Date()
    );

    await QueuesTask.findByIdAndUpdate<KnownQueuesTaskDocument<TASK_NAMES.NOTIFY>>(newTask?._id, {
      retryAfter: nextDate,
    });
  }

  private notify(policyReport: HostsReportModel[] | null) {
    if (!policyReport) return null;

    const notifications = this.inventoryApplicationPolicy?.notifications;

    if (notifications && notifications.enabled) {
      notifications.emails.forEach(async (email: string) => {
        const user = await User.findOne({ email });

        if (user && this.inventoryApplicationPolicy) {
          const getAllApplicationPolicyStatusWrapper = (
            filter?: string,
            limit?: number,
            offset?: number,
            sort?: string,
            req?: Record<string, any> | Request
          ) => {
            return getAllApplicationPolicyStatus(
              filter || '',
              limit,
              offset || 0,
              sort || '',
              this.inventoryApplicationPolicy!.id
            );
          };

          const csvContent = await generateExportFileOf(
            { params: { id: this.inventoryApplicationPolicy?.id } },
            '',
            'rportId:1',
            getExportedColumns(user.language || 'en') as Column[],
            'csv',
            getAllApplicationPolicyStatusWrapper
          );

          notifyReport(user, this.inventoryApplicationPolicy, policyReport, csvContent);
        }
      });
    }
  }

  async validate(): Promise<boolean> {
    try {
      const { inventoryApplicationPolicyId: policyId } = this.task.params;

      const inventoryApplicationPolicy = await ApplicationPolicySchema.findById(policyId);

      this.inventoryApplicationPolicy = inventoryApplicationPolicy;
      if (!inventoryApplicationPolicy) throw errors.not_found('ApplicationPolicy');
      if (!inventoryApplicationPolicy.enabled) return false;

      // Check exists enabled group or switch policy to disabled
      const group = await RportGroup.findById(inventoryApplicationPolicy.group);
      if (!group || group.deleted || !group.enabled) {
        await applicationPolicySwitch(policyId, null, { groupDeleted: true });
        return false;
      }

      this.inventoryApplicationPolicyStatus = await ApplicationPolicyStatus.findOne({ policyId });
      if (!this.inventoryApplicationPolicyStatus) return false;

      return true;
    } catch {}

    return false;
  }

  async validationUpdates(isValid: boolean) {
    // Returns the task updates according to the validation state
    if (!isValid && this.inventoryApplicationPolicy) {
      if (!this.inventoryApplicationPolicy.enabled) {
        return {
          cancelled: new Date(),
          status: CANCELLED,
          statusDetail: 'The inventory application policy is disabled',
        };
      }

      const group = await RportGroup.findById(this.inventoryApplicationPolicy.group);
      if (!group || group.deleted || !group.enabled) {
        return {
          cancelled: new Date(),
          status: CANCELLED,
          statusDetail: 'The group of this inventory application policy is disabled or deleted',
        };
      }
    }

    return {};
  }

  async run() {
    try {
      let policyReport = this.inventoryApplicationPolicyStatus?.toJSON().policyReport;
      if (policyReport) {
        policyReport = await updateNamesToPolicyReport(policyReport);
        this.notify(policyReport);
      }

      // Create next task before finish current task
      await this.createNewTask();

      return {
        _id: this.task._id,
        finished: new Date(),
        status: FINISHED,
      };
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }
  }
}
