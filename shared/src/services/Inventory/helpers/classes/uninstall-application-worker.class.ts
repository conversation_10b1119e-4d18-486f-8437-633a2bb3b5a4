import { Types } from 'mongoose';

import { Task } from '@shared/services/Queues/helpers/types';

import { Logger } from '@shared/helpers/classes/logger.class';

import { getAndValidateClientsConnected } from '@shared/services/Rport/helpers/utils/rport-platform.utils';

import AbstractWorker from '@shared/services/Queues/helpers/worker/abstractWorker';
import TasksFunctions from '@shared/services/Queues/helpers/functions/task.function';

import { runUninstallTask } from '../functions';

import { FINISHED, PENDING } from '@shared/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { RportClientId } from '@shared/services/Rport/helpers/types/rport-client.types';

export class UninstallApplicationWorker extends AbstractWorker<
  Task<TASK_NAMES.UNINSTALL_SOFTWARE>
> {
  selectedHosts: (string | RportClientId)[];
  affectedHosts: (string | RportClientId)[];
  job: Types.ObjectId;
  rportIdsConnected: string[] = [];
  rportIdsDisconnected: string[] = [];
  softwareName: string;
  softwareVersion?: string;
  softwareVendor?: string;
  uninstallString: string;

  constructor(task: Task<TASK_NAMES.UNINSTALL_SOFTWARE>) {
    super(task);
    // Setting the task params
    this.job = task.params.job as Types.ObjectId;
    this.selectedHosts = task.params.selectedHosts;
    this.affectedHosts = task.params.affectedHosts;
    this.softwareName = task.params.softwareName;
    this.softwareVersion = task.params.softwareVersion;
    this.softwareVendor = task.params.softwareVendor;
    this.uninstallString = task.params.uninstallString;
  }

  async validate(): Promise<boolean> {
    try {
      const { clientIdsConnected, clientIdsDisconnected } = await getAndValidateClientsConnected(
        this.selectedHosts
      );

      this.rportIdsConnected = clientIdsConnected;
      this.rportIdsDisconnected = clientIdsDisconnected;

      return true;
    } catch (err) {
      Logger.error('Uninstall Worker Error', err);
    }

    return false;
  }

  async run() {
    if (this.rportIdsConnected.length === 0) {
      return {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: 'All clients disconnected',
      };
    }

    // Hosts to uninstall
    const hostsToUninstall = this.selectedHosts.filter((host) =>
      this.rportIdsConnected.includes(host)
    );

    try {
      // Running the uninstall task on the active hosts
      await runUninstallTask(
        hostsToUninstall.map((host) => ({
          rportId: host,
          name: this.softwareName,
          vendor: this.softwareVendor,
          version: this.softwareVersion,
          uninstallString: this.uninstallString,
        })),
        this.job
      );

      // Updating the task
      await TasksFunctions.updateTask(this.task._id, {
        params: {
          ...this.task.params,
          selectedHosts: this.rportIdsDisconnected,
          affectedHosts: [...this.affectedHosts, ...hostsToUninstall],
        },
      });
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }

    const areHostLeft = this.rportIdsDisconnected.length > 0;

    // Returning updated task based on result
    const status = areHostLeft ? PENDING : FINISHED;
    const statusDetail = areHostLeft ? '' : 'Task executed on all selected hosts';
    const finished = areHostLeft ? undefined : new Date();

    return {
      _id: this.task._id,
      finished,
      status,
      statusDetail,
    };
  }
}
