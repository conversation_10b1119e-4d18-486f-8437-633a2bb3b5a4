import { Types } from 'mongoose';

import { InventoryScanPolicy } from '../../schemas/scan_policy.schema';

import AbstractWorker from '@shared/services/Queues/helpers/worker/abstractWorker';
import { Task } from '@shared/services/Queues/helpers/types';

import TasksFunctions from '@shared/services/Queues/helpers/functions/task.function';
import { getOnlineHostsFromGroup } from '@shared/services/Rport/helpers/functions';
import { runFunctionInBatchWithDelay } from '@shared/services/Rport/helpers/utils/rport-clients.utils';
import { checkScanPolicyGroup, executeScan } from '../functions';

import { CANCELLED, PENDING, TaskStatus } from '@shared/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';

export class InventoryScanPolicyWorker extends AbstractWorker<Task<TASK_NAMES.SCAN>> {
  scanPolicyId: string | Types.ObjectId;
  affectedHosts: string[];

  scanCandidates: string[] = [];

  nextTaskStatus: TaskStatus = PENDING;
  nextTaskStatusDetail: string = '';

  constructor(task: Task<TASK_NAMES.SCAN>) {
    super(task);
    this.scanPolicyId = task.params.scanPolicyId;
    this.affectedHosts = task.params.affectedHosts;
  }

  async validate(): Promise<boolean> {
    try {
      // Get Scan Policy
      const scanPolicy = await InventoryScanPolicy.findOne({
        _id: this.scanPolicyId,
        deleted: false,
        enabled: true,
      });

      if (!scanPolicy) {
        // Cancel the current task
        this.nextTaskStatus = CANCELLED;
        this.nextTaskStatusDetail = 'Scan policy not found';
        return false;
      }

      // Validate that the group is still valid
      const isGroupvalid = await checkScanPolicyGroup(scanPolicy);
      if (!isGroupvalid) {
        // Cancel the current task
        this.nextTaskStatus = CANCELLED;
        this.nextTaskStatusDetail = 'Group not found';
        return false;
      }

      // Get the hosts from the group
      const connectedHosts = await getOnlineHostsFromGroup(scanPolicy.group);
      if (!connectedHosts) {
        this.nextTaskStatus = PENDING;
        this.nextTaskStatusDetail = 'No connected hosts';
        return false;
      }
      // Get clients to affect in this task
      const scanCandidates = connectedHosts.filter(
        (host: string) => !this.affectedHosts.includes(host)
      );
      if (scanCandidates.length === 0) {
        this.nextTaskStatus = PENDING;
        this.nextTaskStatusDetail = 'No scan candidates found';
        return false;
      }

      // Set the client ids connected and disconnected
      this.scanCandidates = scanCandidates;

      return true;
    } catch {}

    return false;
  }

  async validationUpdates(isValid: boolean) {
    // If is valid returns nothing
    if (isValid) return { statusDetail: '' };

    // If is not valid, return the task with the new status
    return {
      status: this.nextTaskStatus,
      statusDetail: this.nextTaskStatusDetail,
    };
  }

  async run() {
    try {
      // Run the scan on the connected hosts
      await runFunctionInBatchWithDelay(this.scanCandidates, executeScan, 100, 1000);
      // Update the task with the affected hosts
      await TasksFunctions.updateTask<TASK_NAMES.SCAN>(this.task._id, {
        params: {
          ...this.task.params,
          affectedHosts: [...this.affectedHosts, ...this.scanCandidates],
        },
      });
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }

    return {
      _id: this.task._id,
      retries: this.task.retries + 1,
      status: PENDING,
    };
  }
}
