import AbstractWorker from '@shared/services/Queues/helpers/worker/abstractWorker';

import { Task } from '@shared/services/Queues/helpers/types';

import { getAndValidateClientsConnected } from '@shared/services/Rport/helpers/utils/rport-platform.utils';

import { FINISHED, PENDING } from '@shared/services/Queues/helpers/constants/status';
import { TASK_NAMES } from '@shared/services/Queues/helpers/constants/tasks';
import { executeScan } from '../functions';
export class InventoryScanPolicyOnClientWorker extends AbstractWorker<
  Task<TASK_NAMES.SCAN_CLIENT>
> {
  hostId: string;
  isConnected: boolean = false;

  constructor(task: Task<TASK_NAMES.SCAN_CLIENT>) {
    super(task);
    this.hostId = task.params.hostId;
  }

  async validate(): Promise<boolean> {
    try {
      const { clientIdsConnected } = await getAndValidateClientsConnected([this.hostId]);

      const isHostConnected = clientIdsConnected.includes(this.hostId);

      this.isConnected = isHostConnected;

      return isHostConnected;
    } catch {}

    return false;
  }

  async run() {
    try {
      await executeScan([this.hostId]);

      return {
        _id: this.task._id,
        finished: new Date(),
        status: FINISHED,
        statusDetail: 'Inventory Scan Started Successfully',
      };
    } catch (error) {
      throw {
        _id: this.task._id,
        retries: this.task.retries + 1,
        status: PENDING,
        statusDetail: error?.toString(),
      };
    }
  }
}
