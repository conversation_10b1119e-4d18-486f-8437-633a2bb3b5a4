import { Types } from 'mongoose';

import { Task } from '../../schemas';

// import de funciones de task
import { rtrGroupTaskResultHandler } from '@shared/services/Bot/actions/';
import { executeRtr } from '@shared/services/CrowdStrike/helpers/functions';
import { RtrGroupTaskModel, RtrDefaultAction } from '../models';

export const queueTaskHandler = async () => {
  // busco todas las tareas y voy ejecutando cada una
  const queue = await Task.find();

  if (!queue.length) {
    return `No tasks found`;
  }

  // teniendo todas las tareas se envian a sus respectivos lugares

  // esta se espera ya que como existe la posiblidad de que falle dentro de la funcion se espera a que pase por los 2 errores posibles
  // el manejo de errores de cada funcion se da por separado ya que no seran throw errors si no funciones para informar por los medios
  await Promise.all(queue.map(async (task) => task && (await taskRouterHandler(task.id))));

  return `Tasks executed`;
};

const taskRouterHandler = async (taskId: Types.ObjectId) => {
  // aca envio la task a su respectiva funcion
  // no se espera respuesta, las funciones se encargaran del manejo de errores dentro de estas
  const task = await Task.findById(taskId);
  if (!task) {
    console.error(`Task with ID ${taskId} not found`);
  } else {
    switch (task.type) {
      // aca se listan todas las posibles tareas que se puedan enviar a cola
      case 'rtrGroup':
        const rtrData = task.config as RtrGroupTaskModel;
        rtrGroupTaskResultHandler(
          rtrData.sessionId,
          rtrData.totalHost,
          rtrData.rtrSessions,
          rtrData.consoleId
        );
        break;
      case 'default-action':
        const rtrAction = task.config as RtrDefaultAction;
        executeRtr(rtrAction.actionId, rtrAction.assetId);
      default:
        break;
    }

    // elimino la tarea ejecutada
    await task.deleteOne();
  }
};

export const pushTaskToQueue = async (type: string, wait: number, config: {}) => {
  // para agregar a cola el timestamp que
  const timestamp = new Date().getTime() + wait;

  // con esto creo una task en la cola
  await Task.create({
    type,
    timestamp,
    config,
  });
};
