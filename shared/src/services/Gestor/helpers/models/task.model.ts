import { Types } from 'mongoose';

export interface TaskModel {
  type: string;
  timestamp: Date;
  config: object | RtrTaskModel | RtrGroupTaskModel | RtrDefaultAction; // se deberan ir agregando los tipos de
}

export interface RtrTaskModel {
  cloudRequestId: {
    assetId: string;
    cloudRequestId: string;
  };
  consoleId: Types.ObjectId;
}

export interface RtrGroupTaskModel {
  sessionId: number;
  command: string;
  totalHost: number;
  rtrSessions: {
    cloudRequestIds: {
      assetId: string;
      cloudRequestId: string;
    }[];
    errors: string[];
  };
  consoleId: Types.ObjectId;
}

export interface RtrDefaultAction {
  actionId: Types.ObjectId;
  assetId: string;
  assetName: string;
}
