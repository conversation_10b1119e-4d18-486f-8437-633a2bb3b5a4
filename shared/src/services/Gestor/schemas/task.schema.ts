import { Document, Schema, model, Types } from 'mongoose';

import { TaskModel } from '../helpers/models';

const taskSchema = new Schema<TaskModel>({
  type: { type: String, required: true },
  timestamp: { type: Date, required: true },
  config: { type: Object, required: true },
});

export type TaskDocument = (TaskModel & Document & { _id: Types.ObjectId }) | null;
export const Task = model<TaskModel>('Task', taskSchema);
