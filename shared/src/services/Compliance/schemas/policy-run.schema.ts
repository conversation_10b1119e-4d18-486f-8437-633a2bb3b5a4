import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { PolicyRunModel } from '../helpers/models';

const policyRunSchema = schemaFactory({
  policy: {
    type: Types.ObjectId,
    ref: 'Policy',
    required: true,
  },
  started_at: {
    type: Date,
    required: false,
  },
  finished_at: {
    type: Date,
    required: false,
  },
  mode: {
    type: String,
    enum: ['MANUAL', 'AUTO'],
    default: 'MANUAL',
  },
  canceled: {
    type: Boolean,
    default: false,
    required: false,
  },
  running: {
    type: Boolean,
    default: true,
    required: false,
  },
});

policyRunSchema.index({ policy: 1 });
policyRunSchema.index({ started_at: 1 });
policyRunSchema.index({ finished_at: 1 });
policyRunSchema.index({ mode: 1 });
policyRunSchema.index({ canceled: 1 });
policyRunSchema.index({ running: 1 });

export type PolicyRunDocument = (PolicyRunModel & Document & { _id: Types.ObjectId }) | null;
export const PolicyRun = modelFactory<PolicyRunModel>('PolicyRun', policyRunSchema);
