import { Types } from 'mongoose';

import { ColumnFormat, ExportFormat, ExportJobStatus } from '@shared/constants/export-job';

export interface ExportJobColumn {
  id: string;
  order: number;
  title: string;
  format: ColumnFormat;
  formatParams?: object;
  field?: string;
  fields?: string[];
  default?: string;
  translations?: object;
}

export interface ExportJobConfig {
  format: ExportFormat;
  filter: string;
  sort?: string;
  columns: ExportJobColumn[];
}

export interface ExportJobModel {
  userId: Types.ObjectId;
  entityType: string;
  config: ExportJobConfig;
  status: ExportJobStatus;
  totalRecords?: number;
  fileName: string;
  completedAt?: Date;
  error?: string;
  cancellable?: boolean;
  id?: Types.ObjectId;
  _id?: Types.ObjectId;
}
