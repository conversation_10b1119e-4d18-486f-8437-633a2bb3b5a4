import { Types } from 'mongoose';

import { SchemaBasicModel } from '@shared/models';

import { ROLES } from '../helpers/constants';

export interface InvitationModel extends SchemaBasicModel {
  // User Fields
  email: string;
  name: string;
  role: (typeof ROLES)[number];
  // Inviter Fields
  createdBy: string;
  updatedBy: string;
  // Invitation Validation Fields
  status: (typeof INVITATION_STATUS)[number];
  token: string;
  // Tracking Fields
  expiresAt: Date;
  revokedBy?: Types.ObjectId;
  revokedAt?: Date;
  acceptedAt?: Date;
  // Static Methods
  generateToken: (bytes?: number, maxAttempts?: number) => Promise<string>;
}

export const INVITATION_STATUS = ['pending', 'accepted', 'revoked', 'expired'] as const;
