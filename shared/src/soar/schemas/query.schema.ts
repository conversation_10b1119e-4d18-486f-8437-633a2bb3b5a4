import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { FilterRequiredFields, QueryModel, Filter } from '../helpers/models/query.model';

import {
  validQueryIdentifiers,
  MAX_QUERIES_PER_TABLE,
  MAX_FILTERS_PER_QUERY,
  MIN_FILTERS_PER_QUERY,
} from '../helpers/constants/query.constant';
import { errors } from '@shared/utils/app-errors';

const querySchema = schemaFactory({
  identifier: { type: String, required: true, enum: validQueryIdentifiers },
  name: { type: String, required: true },
  filters: {
    type: Array,
    required: true,
    validate: {
      validator: (queries: Filter[]) =>
        queries.length >= MIN_FILTERS_PER_QUERY &&
        queries.length <= MAX_FILTERS_PER_QUERY &&
        queries.every((query) =>
          FilterRequiredFields.every((field) => query.hasOwnProperty(field))
        ),
      message: `Queries must have between ${MIN_FILTERS_PER_QUERY} and ${MAX_FILTERS_PER_QUERY} filters, each containing the required fields.`,
    },
  },
  provided: { type: Boolean, default: false },
  user: { type: Types.ObjectId, required: false, ref: 'User' },
});

// Middleware pre-save to limit the number of queries per table
querySchema.pre<QueryModel & Document>('save', async function (this: any) {
  // Count the number of queries for the current table
  const count = await this.model('Query').countDocuments({
    identifier: this.identifier,
    user: this.user,
  });

  // Check if the limit is reached
  if (count >= MAX_QUERIES_PER_TABLE) {
    throw errors.max_entities_tenant('queries');
  }
});

querySchema.index({ identifier: 1 });
querySchema.index({ name: 1 });
querySchema.index({ user: 1 });
querySchema.index({ provided: 1 });
querySchema.index({ identifier: 1, user: 1 });
querySchema.index({ provided: 1, identifier: 1 });

export type QueryDocument = (QueryModel & Document & { _id: Types.ObjectId }) | null;
export const Query = modelFactory<QueryModel>('Query', querySchema);
