import { Document, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';
import modelFactory from '@shared/utils/model-factory';

import { ColumnFormat, ExportFormat, ExportJobStatus } from '@shared/constants/export-job';
import { ExportJobModel } from '../models/export-job.model';

const exportJobSchema = schemaFactory({
  userId: { type: Types.ObjectId, required: true, ref: 'User' },
  entityType: { type: String, required: true },
  config: {
    type: {
      format: { type: String, required: true, enum: ExportFormat },
      filter: { type: String, default: '' },
      sort: { type: String },
      columns: {
        type: [
          {
            id: { type: String, required: true },
            order: { type: Number, required: true },
            title: { type: String, required: true },
            format: {
              type: String,
              required: true,
              enum: ColumnFormat,
            },
            formatParams: { type: Object },
            field: { type: String },
            fields: { type: [String] },
            default: { type: String },
            translations: { type: Object },
          },
        ],
        required: true,
      },
    },
    required: true,
  },
  status: {
    type: String,
    required: true,
    enum: ExportJobStatus,
    default: 'pending',
  },
  totalRecords: { type: Number },
  fileName: { type: String, required: true },
  completedAt: { type: Date },
  error: { type: String },
});

exportJobSchema.index({ userId: 1 });
exportJobSchema.index({ status: 1 });
exportJobSchema.index({ entityType: 1 });

export type ExportJobDocument = (ExportJobModel & Document & { _id: Types.ObjectId }) | null;
export const ExportJob = modelFactory<ExportJobModel>('ExportJob', exportJobSchema);
