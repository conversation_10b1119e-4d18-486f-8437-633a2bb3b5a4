import { Document, model, Types } from 'mongoose';

import schemaFactory from '@shared/utils/schema-factory';

import { FineGrainedTokenModel } from '../models/fine-grained-token.model';

const fineGrainedTokenSchema = schemaFactory({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  prefix: {
    type: String,
    required: true,
    trim: true,
    unique: true,
  },
  permissions: {
    type: [String],
    required: true,
    default: [],
  },
  user: { type: Types.ObjectId, required: true, ref: 'User' },
  expiresAt: { type: Date, default: null },
});

// User and token name combination must be unique, don't count the deleted
fineGrainedTokenSchema.index(
  { name: 1, user: 1 },
  { unique: true, partialFilterExpression: { deleted: false } }
);
fineGrainedTokenSchema.index({ _id: 1, deleted: 1 }, { unique: true });

export type FineGrainedTokenDocument = (FineGrainedTokenModel & Document) | null;
export const FineGrainedToken = model<FineGrainedTokenModel>(
  'FineGrainedToken',
  fineGrainedTokenSchema
);
