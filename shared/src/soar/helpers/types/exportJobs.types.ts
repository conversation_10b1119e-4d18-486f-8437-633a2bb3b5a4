import { Types } from 'mongoose';

import { ExportFormat, ExportJobStatus } from '@shared/constants/export-job';
import { ExportJobConfig, ExportJobModel } from '../../models/export-job.model';

export interface CreateJobParams {
  userId: Types.ObjectId | string;
  entityType: string;
  config: ExportJobConfig;
  fileName: string;
}

export interface UpdateJobParams {
  status?: ExportJobStatus;
  totalRecords?: number;
  completedAt?: Date;
  error?: string;
}

export interface JobHistoryResponse {
  jobs: ExportJobModel[];
  total: number;
}

export interface ExportJobMessage {
  jobId: string;
  userId: string;
  tenantId: string;
  entityType: string;
  format: ExportFormat;
  filter: string;
  sort?: string;
  columns: any[];
  fileName: string;
  locale?: string;
  timestamp: number;
  retryCount?: number;
}
