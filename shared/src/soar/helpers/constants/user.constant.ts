import { modules } from '../../../../../service.manifest.json';

export const ADMIN = 'ADMIN';
export const MANAGER = 'MANAGER';
export const USER_MANAGER = 'USER_MANAGER';
export const USER = 'USER';
export const TELEGRAM_MODERATOR = 'TELEGRAM_MODERATOR';
export const TELEGRAM_USER = 'TELEGRAM_USER';

export const ROLES = [
  ADMIN,
  MANAGER,
  USER_MANAGER,
  USER,
  TELEGRAM_MODERATOR,
  TELEGRAM_USER,
] as const;

export const ROLE_CONFIG: Record<string, any> = {
  [ADMIN]: {
    isAdmin: true,
    isManager: false,
    isUserManager: false,
    isFrontendUser: true,
    isModerator: true,
  },
  [MANAGER]: {
    isAdmin: false,
    isManager: true,
    isUserManager: false,
    isFrontendUser: true,
    isModerator: false,
  },
  [USER_MANAGER]: {
    isAdmin: false,
    isManager: false,
    isUserManager: true,
    isFrontendUser: true,
    isModerator: false,
  },
  [USER]: {
    isAdmin: false,
    isManager: false,
    isUserManager: false,
    isFrontendUser: true,
    isModerator: false,
  },
  [TELEGRAM_MODERATOR]: {
    isAdmin: false,
    isManager: false,
    isUserManager: false,
    isFrontendUser: false,
    isModerator: true,
  },
  [TELEGRAM_USER]: {
    isAdmin: false,
    isManager: false,
    isUserManager: false,
    isFrontendUser: false,
    isModerator: false,
  },
};

const USER_MANAGER_DEFAULT_MODULES: string[] = [
  'overview',
  'host-management',
  'deployment',
  'level-of-proactivity',
];

const USER_DEFAULT_MODULES: string[] = [
  'overview',
  'host-management',
  'deployment',
  'level-of-proactivity',
];

const TELEGRAM_MODERATOR_DEFAULT_MODULES: string[] = [
  'alienvault',
  'pentestools',
  'pwned',
  'virustotal',
  'lambdas',
  'mx-tools',
  'crowdstrike',
];

const TELEGRAM_USER_DEFAULT_MODULES: string[] = [
  'alienvault',
  'pentestools',
  'pwned',
  'virustotal',
  'lambdas',
  'mx-tools',
];

export const DEFAULT_MODULES_BY_ROLE: Record<string, string[]> = {
  [USER_MANAGER]: USER_MANAGER_DEFAULT_MODULES,
  [USER]: USER_DEFAULT_MODULES,
  [TELEGRAM_MODERATOR]: TELEGRAM_MODERATOR_DEFAULT_MODULES,
  [TELEGRAM_USER]: TELEGRAM_USER_DEFAULT_MODULES,
};

const getFunctionalitiesByRole = (role: string): string[] => {
  const rolModules = DEFAULT_MODULES_BY_ROLE[role];
  const functionalitiesSet = new Set<string>();

  for (const moduleInternalName of rolModules) {
    const module = modules.find(({ internalName }) => internalName === moduleInternalName);

    if (module) {
      for (const functionality of module.functionalities) {
        functionalitiesSet.add(functionality);
      }
    }
  }

  const functionalities = Array.from(functionalitiesSet);
  return functionalities;
};

export const DEFAULT_FUNCTIONALITIES: Record<string, string[]> = {
  [USER_MANAGER]: getFunctionalitiesByRole(USER_MANAGER),
  [USER]: getFunctionalitiesByRole(USER),
  [TELEGRAM_MODERATOR]: getFunctionalitiesByRole(TELEGRAM_MODERATOR),
  [TELEGRAM_USER]: getFunctionalitiesByRole(TELEGRAM_USER),
};
