import { FilterType, QueryFilterModel, RelativeType, SelectRelative } from '@shared/models';

import { buildExtraFilters, buildFilterObject } from '@shared/helpers/classes/schema-utils.class';
import { Functionality, UserDocument } from '@shared/schemas';

import { NewUserRequestModel, UserUpdateRequestModel } from '@shared/models/user.model';

import {
  ADMIN,
  MANAGER,
  TELEGRAM_MODERATOR,
  TELEGRAM_USER,
  USER,
  USER_MANAGER,
  ROLES,
  DEFAULT_FUNCTIONALITIES,
} from '../constants';

export const isAllowToOperate = (
  requester: UserDocument,
  user: UserDocument | NewUserRequestModel | UserUpdateRequestModel
) => {
  const requesterRole = ROLES.indexOf(getUserRole(requester));
  const otherUserRole = ROLES.indexOf(getUserRole(user));

  // Less than since roles are sorted desc.
  return requesterRole <= otherUserRole;
};

export const getUserFilters = (isAdmin: boolean = false) => {
  const options = [
    { key: TELEGRAM_MODERATOR, value: 'isModerator' },
    { key: MANAGER, value: 'isManager' },
    { key: USER_MANAGER, value: 'isUserManager' },
    { key: USER, value: 'isFrontendUser' },
  ];
  if (isAdmin) options.push({ key: ADMIN, value: 'isAdmin' });
  return {
    role: {
      type: FilterType.SELECT,
      relatives: Object.values(SelectRelative),
      options: options,
    },
  };
};

export const parseUserFilter = (filterObject: QueryFilterModel) => {
  return buildExtraFilters(filterObject, customQueryBuilder);
};

const customQueryBuilder = (key: string, relative: RelativeType, value: string) => {
  let query;
  if (key === 'role') {
    query = roleConditionMaker(relative, value);
  }
  return query;
};

const roleConditionMaker = (relative: RelativeType, value: string) => {
  // This filter does not allow to filter by role correctly
  switch (value) {
    case 'isAdmin':
      return { [value]: buildFilterObject(relative, 'true') };
    case 'isModerator':
      return { [value]: buildFilterObject(relative, 'true') };
    case 'isManager':
      return { [value]: buildFilterObject(relative, 'true') };
    case 'isUserManager':
      return { [value]: buildFilterObject(relative, 'true') };
    case 'isFrontendUser':
      return { [value]: buildFilterObject(relative, 'true') };
    default:
      return {};
  }
};

export const getUserRole = (
  user: UserDocument | NewUserRequestModel | UserUpdateRequestModel
):
  | typeof ADMIN
  | typeof MANAGER
  | typeof USER_MANAGER
  | typeof USER
  | typeof TELEGRAM_MODERATOR
  | typeof TELEGRAM_USER => {
  if (user.isAdmin && user.isFrontendUser) {
    return ADMIN;
  } else if (user.isManager && user.isFrontendUser) {
    return MANAGER;
  } else if (user.isUserManager && user.isFrontendUser) {
    return USER_MANAGER;
  } else if (user.isFrontendUser) {
    return USER;
  } else if (user.isModerator) {
    return TELEGRAM_MODERATOR;
  } else {
    return TELEGRAM_USER;
  }
};

export const getUserPermissions = async (user: UserDocument): Promise<string[]> => {
  // Get enabled functionalities for the tenant
  const enabledPermissions = await Functionality.getEnabledPermissions();

  // Get user Role
  const role = getUserRole(user);
  // If role manager or admin, return all permissions
  if (role === ADMIN || role === MANAGER) {
    return enabledPermissions;
  }

  if (!user.permissions || user.permissions.length === 0) {
    return [];
  }

  const userPermissionSet = new Set(user.permissions);

  // If role is user manager, return user manager permissions but only the ones enabled
  return enabledPermissions.filter((permission) => userPermissionSet.has(permission));
};

export const getDefaultUserPermissions = async (
  role: (typeof ROLES)[number]
): Promise<string[]> => {
  // If role manager or admin, return all permissions
  if (role === ADMIN || role === MANAGER) {
    return await Functionality.getEnabledPermissions();
  }

  // Get functionalities based on the role
  const functionalities: string[] = DEFAULT_FUNCTIONALITIES[role];

  // Add moderator functionalities when is frontend user but still is moderator
  if (role === TELEGRAM_MODERATOR) {
    functionalities.push(...DEFAULT_FUNCTIONALITIES[TELEGRAM_MODERATOR]);
  }

  if (functionalities.length === 0) return [];

  // Return all permissions
  return await Functionality.getEnabledPermissions(functionalities);
};
