import { getS3Client } from '../services';

import { S3Client, ListObjectsCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';

import { CDN_S3_BUCKET } from '@shared/constants/env';

const getFilesOlderThanOneHour = async (client: S3Client, bucketName: string) => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);

  const listParams = {
    Bucket: bucketName,
    Prefix: 'temp/',
  };

  const listedObjects = await client.send(new ListObjectsCommand(listParams));

  return (
    listedObjects.Contents?.filter((obj) => {
      // Validating the split
      if (!obj.Key || !obj.Key.includes('-')) {
        return false;
      }

      // Getting timestamp
      const timestampPart = obj.Key.split('/')[1].split('-')[0];
      const timestamp = parseInt(timestampPart, 10);

      // Validating timestamp
      if (isNaN(timestamp)) {
        return false;
      }

      // Checking if we should remove it
      const fileDate = new Date(timestamp);
      return fileDate < oneHourAgo;
    }) || []
  );
};

export const deleteOldFiles = async () => {
  if (CDN_S3_BUCKET) {
    const client = getS3Client();

    // Getting files to remove
    const filesToDelete = await getFilesOlderThanOneHour(client, CDN_S3_BUCKET);

    // Removing one file at the time
    for (const file of filesToDelete) {
      const deleteParams = {
        Bucket: CDN_S3_BUCKET,
        Key: file.Key,
      };

      await client.send(new DeleteObjectCommand(deleteParams));
    }

    return `${filesToDelete.length} expired files removed from CDN`;
  } else {
    return 'CDN Bucket not configured. Nothing to do.';
  }
};
