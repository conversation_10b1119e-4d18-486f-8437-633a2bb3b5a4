import { errors } from '@shared/utils/app-errors';

import { getClientsFromRport } from '../../../services/Rport/helpers/functions/client.functions';

import { ExportJobService } from '../services/exportJob.services';
import { ExportJobConfig } from '../../models/export-job.model';
// import { getAllAvailablePatches } from '@shared/services/Patching/controllers/available-patches.controllers';

const standardizeResponse = (fn: (...args: any[]) => Promise<{ size: number; results: any[] }>) => {
  return async (filters: string, limit: number, offset: number, sort?: string) => {
    const { size, results } = await fn(filters, limit, offset, sort);
    return { data: results, meta: { count: size, total: size, resources: results.length } };
  };
};

const EXPORT_DEFINITIONS = {
  clients: getClientsFromRport,
  // 'available-patches': standardizeResponse(getAllAvailablePatches),
} as const;

export const EXPORT_SERVICES = Object.fromEntries(
  Object.entries(EXPORT_DEFINITIONS).map(([exportKey, fn]) => [exportKey, { key: exportKey, fn }])
);

export const SERVICES_REGISTERED = Object.fromEntries(
  Object.keys(EXPORT_DEFINITIONS).map((exportKey) => [
    exportKey.toUpperCase().replace(/-/g, '_'),
    exportKey,
  ])
);

export const exportRegistry = EXPORT_DEFINITIONS;

const generateDefaultFileName = (entityType: string, format: string): string => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `${entityType}_export_${timestamp}.${format}`;
};

export const registerExportJob = async (
  entityType: (typeof SERVICES_REGISTERED)[keyof typeof SERVICES_REGISTERED],
  config: ExportJobConfig & { userId?: string; fileName?: string }
) => {
  if (!entityType) {
    throw errors.input_not_valid('Entity type is required for async exports');
  }

  if (!Object.values(SERVICES_REGISTERED).includes(entityType)) {
    throw errors.input_not_valid(
      `Invalid entityType: ${entityType}. Must be a registered export service.`
    );
  }

  if (!config.userId) {
    throw errors.input_not_valid('User authentication required for async exports');
  }

  const job = await ExportJobService.create({
    userId: config.userId,
    entityType,
    config: { ...config },
    fileName: config.fileName || generateDefaultFileName(entityType, config.format),
  });

  const response = {
    id: job._id,
    status: job.status,
    message: 'Export job created successfully',
  };

  return response;
};
