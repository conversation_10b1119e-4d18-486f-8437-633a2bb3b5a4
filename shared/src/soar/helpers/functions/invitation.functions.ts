import { Types } from 'mongoose';

import { Invitation } from '@shared/soar/schemas/invitation.schema';

import { errors } from '@shared/utils/app-errors';
import { Notifier } from '@shared/helpers/classes/notifier.class';
import { t } from '@shared/utils/translation/translator';
import { BATUTA_FRONTEND_BASE_URL } from '@shared/constants/env';

export const checkAndExpiredInvitations = async () => {
  const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  Invitation.updateMany(
    { status: 'pending', createdAt: { $lt: sevenDaysAgo } },
    { $set: { status: 'expired' } }
  );
  return 'Expired invitations checked and updated successfully.';
};

export const checkAndExpireInvitation = async (invitationId: Types.ObjectId | string) => {
  try {
    const invitation = await Invitation.findById(invitationId);
    if (!invitation) throw errors.not_found('Invitation');

    // Current Date
    const currentDate = new Date();

    // Check if the invitation is pending and if is has expired
    if (invitation.status === 'pending' && invitation.expiresAt <= currentDate) {
      // Update the status to expired
      await Invitation.updateOne(
        { _id: invitation._id },
        { $set: { status: 'expired', updatedAt: currentDate } }
      );
      throw errors.expired('Invitation');
    }

    return invitation;
  } catch (err) {
    throw err;
  }
};

export const sendInvitationEmail = async (
  username: string,
  email: string,
  inviterName: string,
  token: string
) => {
  Notifier.sendEmail({
    to: email,
    subject: 'Welcome to Batuta! Complete your account setup',
    templateName: 'newInvitation',
    templateValues: {
      invitedUser: username,
      adminName: inviterName,
      title: t('email', 'en', 'NEW_INVITATION_TITLE'),
      body1: t('email', 'en', 'NEW_INVITATION_BODY_1'),
      linkText: t('email', 'en', 'NEW_INVITATION_LINK_TEXT'),
      linkURL: `${BATUTA_FRONTEND_BASE_URL}/invitation?token=${token}`,
      linkNotWorking: t('email', 'en', 'NEW_INVITATION_LINK_NOT_WORKING'),
      warnin1: t('email', 'en', 'NEW_INVITATION_LINK_WARNING_1'),
      warning2: t('email', 'en', 'NEW_INVITATION_LINK_WARNING_2'),
    },
    language: 'en',
  });
};
