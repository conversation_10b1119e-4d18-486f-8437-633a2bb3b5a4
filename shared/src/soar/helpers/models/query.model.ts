import { Types } from 'mongoose';

import { SchemaBasicModel } from '@shared/models';

export interface Filter {
  field: string;
  operator: string;
  value: string;
  key: string;
}
export const FilterRequiredFields = ['field', 'operator', 'value', 'key'];

export interface QueryModel extends SchemaBasicModel {
  identifier: string;
  name: string;
  filters: Filter[];
  provided: boolean;
  user?: Types.ObjectId;
}
