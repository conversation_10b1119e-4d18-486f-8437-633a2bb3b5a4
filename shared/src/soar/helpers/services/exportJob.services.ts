import { Types } from 'mongoose';

import { GetAllQuery } from '@shared/types';
import {
  CreateJobParams,
  ExportJobMessage,
  JobHistoryResponse,
  UpdateJobParams,
} from '../types/exportJobs.types';
import { errors } from '@shared/utils/app-errors';
import { ExportJobModel } from '../../models/export-job.model';
import { ExportJob } from '../../schemas/exportJob.schema';

import { KafkaService } from '@shared/helpers/classes/queue-worker.class';
import { ExportJobStatus } from '@shared/constants/export-job';
import { EXPORT_JOB_KAFKA_TOPIC_NAME, SOAR_ID } from '@shared/constants/env';

export class ExportJobService {
  static async create(params: CreateJobParams): Promise<ExportJobModel> {
    const jobData: Partial<ExportJobModel> = {
      userId: new Types.ObjectId(params.userId),
      entityType: params.entityType,
      config: params.config,
      status: ExportJobStatus.Pending,
      fileName: params.fileName,
    };

    const job = await ExportJob.create(jobData);
    const kafkaMessage: ExportJobMessage = {
      jobId: job._id.toString(),
      userId: params.userId.toString(),
      tenantId: SOAR_ID,
      entityType: params.entityType,
      fileName: params.fileName,
      timestamp: Date.now(),
      ...params.config,
    };

    const producer = await KafkaService.getInstance();
    await producer.sendMessage(EXPORT_JOB_KAFKA_TOPIC_NAME, kafkaMessage.jobId, kafkaMessage, {
      timestamp: kafkaMessage.timestamp.toString(),
      entityType: kafkaMessage.entityType,
      format: kafkaMessage.format,
    });

    return job;
  }

  static async get(_id: string, userId: string): Promise<ExportJobModel> {
    const job = await ExportJob.findOne({ _id, userId, deleted: false });
    if (!job) throw errors.not_found('Export job');

    return { ...job.toObject(), cancellable: this.isCancellable(job) };
  }

  static async update(
    _id: string,
    userId: string,
    params: UpdateJobParams
  ): Promise<ExportJobModel> {
    const updateData = {
      ...params,
      updatedAt: new Date(),
    };

    const job = await ExportJob.findOneAndUpdate({ _id, userId, deleted: false }, updateData, {
      new: true,
    });
    if (!job) throw errors.not_found('Export job');

    return job;
  }

  static async complete(_id: string, userId: string): Promise<ExportJobModel> {
    return this.update(_id, userId, {
      status: ExportJobStatus.Completed,
      completedAt: new Date(),
    });
  }

  static async fail(_id: string, userId: string, error: string): Promise<ExportJobModel> {
    return this.update(_id, userId, {
      status: ExportJobStatus.Failed,
      error,
    });
  }

  static isCancellable(job: ExportJobModel): boolean {
    const cancellableStates: ExportJobStatus[] = [
      ExportJobStatus.Pending,
      ExportJobStatus.Processing,
    ];
    return cancellableStates.includes(job.status);
  }

  static async isCancelled(id: string, userId: string): Promise<boolean> {
    const job = await this.get(id, userId);
    return job.status === ExportJobStatus.Cancelled;
  }

  static async cancel(_id: string, userId: string): Promise<ExportJobModel> {
    const job = await this.get(_id, userId);

    if (job.userId.toString() !== userId) {
      throw errors.not_permitted('Not authorized to cancel this job');
    }

    if (!this.isCancellable(job)) {
      throw errors.input_not_valid(`Cannot cancel job in status: ${job.status}`);
    }

    return this.update(_id, userId, {
      status: ExportJobStatus.Cancelled,
      error: 'Cancelled by user',
    });
  }

  static async getAllJobs(userId: string, params: GetAllQuery): Promise<JobHistoryResponse> {
    const { offset = 0, limit = 100, filter = '', sort = '' } = params;

    const [query, sortQuery] = ExportJob.parseFilter(filter, sort);
    const fullQuery = {
      ...query,
      userId: new Types.ObjectId(userId),
      deleted: false,
    };

    const [jobs, total] = await Promise.all([
      await ExportJob.find(fullQuery)
        .sort(sortQuery ?? '-createdAt')
        .limit(limit ?? 100)
        .skip(offset ?? 0),
      await ExportJob.countDocuments(fullQuery),
    ]);

    return { jobs, total };
  }

  static async delete(_id: string, userId: string): Promise<ExportJobModel> {
    const job = await this.get(_id, userId);
    if (job.userId.toString() !== userId) throw errors.not_permitted();

    await ExportJob.findOneAndUpdate(
      { _id },
      {
        deleted: true,
        updatedAt: new Date(),
      }
    );

    return job;
  }

  static async cleanupOldJobs(retentionDays: number): Promise<number> {
    const retentionDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);

    const result = await ExportJob.updateMany(
      {
        createdAt: { $lt: retentionDate },
        status: {
          $in: [ExportJobStatus.Completed, ExportJobStatus.Failed, ExportJobStatus.Cancelled],
        },
        deleted: false,
      },
      {
        deleted: true,
        updatedAt: new Date(),
      }
    );

    return result.modifiedCount;
  }

  static async markAsDownloaded(id: string, userId: string): Promise<ExportJobModel> {
    const job = await this.get(id, userId);

    if (job.status !== ExportJobStatus.Completed) {
      throw errors.input_not_valid(`Cannot mark job as downloaded. Current status: ${job.status}`);
    }

    return this.update(id, userId, {
      status: ExportJobStatus.Downloaded,
    });
  }
}
