import { Readable } from 'stream';
import {
  S3Client,
  PutObjectCommand,
  ListO<PERSON>sCommand,
  DeleteObjectCommand,
  CopyObjectCommand,
  ObjectCannedACL,
  GetObjectCommand,
  HeadObjectCommand,
} from '@aws-sdk/client-s3';
import { NodeJsClient } from '@smithy/types';

import { errors } from '@shared/utils/app-errors';

import {
  CDN_S3_BASE_URL,
  CDN_S3_BUCKET,
  CDN_S3_REGION,
  DIGITAL_OCEAN_ACCESS_KEY_ID,
  DIGITAL_OCEAN_SECRET_ACCESS_KEY,
} from '@shared/constants/env';

export const getS3Client = () => {
  return new S3Client({
    forcePathStyle: false,
    endpoint: CDN_S3_BASE_URL,
    region: CDN_S3_REGION,
    credentials: {
      accessKeyId: DIGITAL_OCEAN_ACCESS_KEY_ID,
      secretAccessKey: DIGITAL_OCEAN_SECRET_ACCESS_KEY,
    },
  }) as NodeJsClient<S3Client>;
};

export const uploadFile = async (filename: string, buffer: Buffer, folder = 'temp') => {
  const client = getS3Client();

  //File extension extraction
  const extension = filename.split('.').pop();

  // Cleaning the file name
  let cleanedFileName = filename
    .replace(/\.[^/.]+$/, '') // Remove the extension
    .replace(/[^a-z0-9_\-\.]/gi, '') // Remove special characters
    .replace(/ /g, '_') // Replace spaces with underscores
    .toLowerCase(); // Convert to lowercase

  // Limit the length of the file name
  cleanedFileName = cleanedFileName.substring(0, 250);

  // Reconstitutes the file name with the extension
  cleanedFileName = `${cleanedFileName}.${extension}`;

  // File path with clean name
  const filePath = `${folder}/${Date.now().toString()}-${cleanedFileName}`;

  const bucketParams = {
    Bucket: CDN_S3_BUCKET,
    Key: filePath,
    Body: buffer,
    ACL: ObjectCannedACL.public_read,
  };

  try {
    await client.send(new PutObjectCommand(bucketParams));
    return filePath;
  } catch (err) {
    throw err;
  }
};

export const downloadFileFromS3 = async (key: string) => {
  const client = getS3Client();

  const bucketParams = {
    Bucket: CDN_S3_BUCKET,
    Key: key,
  };

  let command: GetObjectCommand;

  try {
    command = new GetObjectCommand(bucketParams);
  } catch (err) {
    throw err;
  }

  try {
    const data = await client.send(command);

    if (!data.Body || !(data.Body instanceof Readable)) {
      throw errors.not_found('File');
    }

    return {
      Body: data.Body,
      ContentType: data.ContentType,
      ContentLength: data.ContentLength,
      Metadata: data.Metadata,
    };
  } catch (err) {
    throw err;
  }
};

export const getFileHeadFromS3 = async (key: string) => {
  const client = getS3Client();

  const bucketParams = {
    Bucket: CDN_S3_BUCKET,
    Key: key,
  };

  const command = new HeadObjectCommand(bucketParams);

  const data = await client.send(command);

  if (!data.Metadata === undefined) {
    throw errors.not_found('File');
  }

  return data;
};

export const getFiles = async (source?: string) => {
  const client = getS3Client();

  const bucketParams = {
    Bucket: CDN_S3_BUCKET,
    Prefix: source ? `${source}/` : undefined,
  };

  try {
    const data = await client.send(new ListObjectsCommand(bucketParams));
    return data;
  } catch (err) {
    throw err;
  }
};

export const moveFile = async (source: string, destination: string) => {
  const client = getS3Client();

  // Copy file from folder to destination
  await client.send(
    new CopyObjectCommand({
      Bucket: CDN_S3_BUCKET,
      CopySource: `${CDN_S3_BUCKET}/${source}`,
      Key: destination,
      ACL: ObjectCannedACL.public_read,
    })
  );

  // Remove original file
  await client.send(
    new DeleteObjectCommand({
      Bucket: CDN_S3_BUCKET,
      Key: source,
    })
  );

  return `https://${CDN_S3_BUCKET}.${CDN_S3_BASE_URL.replace('https://', '')}/${destination}`;
};

export const deleteFile = async (filename: string) => {
  const client = getS3Client();
  const bucketParams = {
    Bucket: CDN_S3_BUCKET,
    Key: filename,
  };
  try {
    const data = await client.send(new DeleteObjectCommand(bucketParams));
    return data;
  } catch (err) {
    throw err;
  }
};

export async function fileInCDN(fileName: string) {
  try {
    const contentLength = (await getFileHeadFromS3(fileName)).ContentLength;
    return contentLength !== undefined && contentLength > 0;
  } catch (_) {
    return false;
  }
}
