{"compilerOptions": {"target": "es2020", "module": "commonjs", "resolveJsonModule": true, "strict": true, "strictPropertyInitialization": false, "moduleResolution": "node", "baseUrl": ".", "paths": {"@shared/*": ["shared/src/*"]}, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./shared/types", "./node_modules/@types"]}, "include": ["shared/types/**/*"], "references": [{"path": "./shared"}, {"path": "./apps/api-users"}, {"path": "./apps/api-worker"}]}