name: Build Image and Push to ECR
on:
  workflow_call:
    inputs:
      ECR_REPOSITORY:
        required: true
        type: string
      AWS_REGION:
        required: true
        type: string
      environment:
        required: true
        type: string
    outputs:
      tag:
        description: 'Tag of the new built docker image'
        value: ${{ jobs.build.outputs.tag }}

jobs:
  build:
    name: Build Image and Push to ECR
    environment: ${{ inputs.environment }}
    permissions:
      id-token: write
      contents: write
    env:
      ECR_REPOSITORY: ${{ inputs.ECR_REPOSITORY }}
      IMAGE_TAG: ${{ github.sha }}
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.set_github_run_number.outputs.outtag }}
    steps:
      - name: Checkout Git Code
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ inputs.AWS_REGION }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Get ECR login information
        id: get-ecr-password
        run: echo "password=$(aws ecr get-login-password)" >> $GITHUB_OUTPUT

      - name: Set GITHUB_RUN_NUMBER
        id: set_github_run_number
        run: |
          echo GITHUB_RUN_NUMBER=$(( GITHUB_RUN_NUMBER + 279 ))>> $GITHUB_ENV
          echo "outtag=batuta-api-users-$((GITHUB_RUN_NUMBER + 279))" >> $GITHUB_OUTPUT
      - name: Build and push
        uses: docker/build-push-action@v1
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        with:
          registry: ${{ steps.login-ecr.outputs.registry }}
          repository: ${{env.ECR_REPOSITORY}}
          username: AWS
          password: ${{ steps.get-ecr-password.outputs.password }}
          add_git_labels: true
          tag_with_ref: true
          tags: 'batuta-api-users-${{env.GITHUB_RUN_NUMBER}},latest-api-users'
          push: true
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: '${{vars.AWS_ACCOUNT_ID}}.dkr.ecr.${{inputs.AWS_REGION}}.amazonaws.com/${{env.ECR_REPOSITORY}}:batuta-api-users-${{env.GITHUB_RUN_NUMBER}}'
          format: 'table'
        env:
          TRIVY_DB_REPOSITORY: public.ecr.aws/aquasecurity/trivy-db
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{  secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_DEFAULT_REGION: ${{inputs.AWS_REGION}}
